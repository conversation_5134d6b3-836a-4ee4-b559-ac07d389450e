#!/usr/bin/env node

/**
 * Complete UI Components Library Setup
 * This script creates a fully functional, production-ready UI components library
 */

const fs = require('fs');
const path = require('path');

// Create proper Tamagui configuration
function createTamaguiConfig() {
  const configContent = `import { createTamagui, createFont } from '@tamagui/core'
import { shorthands } from '@tamagui/shorthands'
import { themes, tokens } from '@tamagui/themes'
import { createInterFont } from '@tamagui/font-inter'

const headingFont = createInterFont({
  size: {
    6: 16,
    7: 18,
    8: 20,
    9: 22,
    10: 24,
  },
  transform: {
    6: 'uppercase',
    7: 'none',
  },
  weight: {
    6: '400',
    7: '700',
  },
  color: {
    6: '$colorFocus',
    7: '$color',
  },
  letterSpacing: {
    5: 2,
    6: 1,
    7: 0,
    8: -1,
    9: -2,
    10: -3,
    12: -4,
    14: -5,
    15: -6,
  },
  face: {
    700: { normal: 'InterBold' },
  },
})

const bodyFont = createInterFont(
  {
    face: {
      700: { normal: 'InterBold' },
    },
  },
  {
    sizeSize: (size) => Math.round(size * 1.1),
    sizeLineHeight: (size) => Math.round(size * 1.1 + (size > 20 ? 10 : 10)),
  }
)

export const config = createTamagui({
  animations: {
    bouncy: {
      type: 'spring',
      damping: 10,
      mass: 0.9,
      stiffness: 100,
    },
    lazy: {
      type: 'spring',
      damping: 20,
      stiffness: 60,
    },
    quick: {
      type: 'spring',
      damping: 20,
      mass: 1.2,
      stiffness: 250,
    },
  },
  shouldAddPrefersColorThemes: true,
  themeClassNameOnRoot: true,
  shorthands,
  fonts: {
    heading: headingFont,
    body: bodyFont,
  },
  themes,
  tokens,
})

export default config

export type Conf = typeof config

declare module '@tamagui/core' {
  interface TamaguiCustomConfig extends Conf {}
}
`;

  fs.writeFileSync(path.join(__dirname, 'src/config/tamagui.config.ts'), configContent, 'utf8');
  console.log('✅ Created Tamagui configuration');
}

// Update main index.ts with proper exports
function updateMainIndex() {
  const indexContent = `/**
 * @hvppyplug/ui-components
 * 
 * A comprehensive React Native UI components library built with Tamagui,
 * designed specifically for South African food delivery applications.
 */

// Core Components
export { Button } from './components/Button'
export { Input } from './components/Input'
export { Card } from './components/Card'
export { Text, Heading } from './components/Typography'
export { Container } from './components/Layout/Container'
export { Stack } from './components/Layout/Stack'

// Form Components
export { 
  FormProvider, 
  FormInput, 
  FormSelect, 
  FormCheckbox, 
  FormRadioGroup, 
  FormSwitch, 
  FormDatePicker, 
  FormActions, 
  FormSection,
  useFormContext 
} from './components/Form'

// Feedback Components
export { Toast, ToastProvider, useToast } from './components/Toast'
export { Loading, Spinner, ProgressBar, LoadingOverlay, Skeleton } from './components/Loading'
export { Alert } from './components/Alert'
export { Modal, ModalProvider, useModal } from './components/Modal'

// Navigation Components
export { Header } from './components/Navigation/Header'
export { TabBar } from './components/Navigation/TabBar'
export { Breadcrumb } from './components/Navigation/Breadcrumb'
export { NavigationButton } from './components/Navigation/NavigationButton'

// Animation Components
export { AnimatedView, FadeIn, FadeOut, SlideIn, SlideOut, ScaleIn, ScaleOut } from './animations'

// Icon System
export { Icon, IconProvider } from './icons'

// Hooks
export { useForm } from './hooks/useForm'
export { useTheme } from './hooks/useTheme'
export { useAnimation } from './hooks/useAnimation'
export { useHaptics } from './hooks/useHaptics'

// Configuration
export { config as tamaguiConfig } from './config/tamagui.config'

// Types (specific exports to avoid conflicts)
export type { 
  ButtonProps, 
  InputProps, 
  CardProps, 
  TextProps, 
  ContainerProps, 
  StackProps,
  IconProps as ComponentIconProps,
  ModalProps as ComponentModalProps,
} from './types/components'

export type {
  FormProviderProps,
  FormInputProps,
  FormSelectProps,
  FormCheckboxProps,
  FormRadioGroupProps,
  FormSwitchProps,
  FormDatePickerProps,
  FormActionsProps,
  FormSectionProps,
  FormValidationSchema,
  FormSubmissionState,
  FormConfig,
  FormFieldValidationRules,
  FormErrorMessage,
  FormFieldState,
  FormContextType,
  FormStepProps,
  FormStepperProps,
} from './types/forms'

// Re-export commonly used Tamagui components
export {
  TamaguiProvider,
  Theme,
  createTamagui,
  styled,
  withStaticProperties,
} from '@tamagui/core'

// Re-export form utilities
export { useForm as useReactHookForm, Controller } from 'react-hook-form'
export { z } from 'zod'
export { zodResolver } from '@hookform/resolvers/zod'

// Utility functions
export * from './utils/colors'
export * from './utils/spacing'
export * from './utils/typography'
export * from './utils/validation'

// Version
export const VERSION = '1.0.0'

// Package info
export const PACKAGE_INFO = {
  name: '@hvppyplug/ui-components',
  version: VERSION,
  description: 'React Native UI components for South African food delivery apps',
  author: 'Augment Agent',
  license: 'MIT',
} as const
`;

  fs.writeFileSync(path.join(__dirname, 'src/index.ts'), indexContent, 'utf8');
  console.log('✅ Updated main index.ts');
}

// Fix all component imports to use @tamagui/core
function fixAllImports() {
  const replacements = [
    {
      from: /import\s*{\s*([^}]+)\s*}\s*from\s*['"]tamagui['"]/g,
      to: 'import { $1 } from \'@tamagui/core\''
    }
  ];

  function processFile(filePath) {
    if (!fs.existsSync(filePath)) return false;
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    replacements.forEach(replacement => {
      const newContent = content.replace(replacement.from, replacement.to);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    return false;
  }

  function processDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) return 0;
    
    const items = fs.readdirSync(dirPath);
    let totalFixed = 0;
    
    items.forEach(item => {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        totalFixed += processDirectory(itemPath);
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
        if (processFile(itemPath)) {
          totalFixed++;
        }
      }
    });
    
    return totalFixed;
  }

  const srcPath = path.join(__dirname, 'src');
  const totalFixed = processDirectory(srcPath);
  console.log(`✅ Fixed imports in ${totalFixed} files`);
}

// Create working styled components
function createStyledComponents() {
  const styledContent = `import { styled } from '@tamagui/core'
import { View, Text as RNText, TouchableOpacity, TextInput, ScrollView } from 'react-native'

// Create basic styled components that work with @tamagui/core
export const XStack = styled(View, {
  name: 'XStack',
  flexDirection: 'row',
  alignItems: 'center',
})

export const YStack = styled(View, {
  name: 'YStack', 
  flexDirection: 'column',
})

export const Stack = styled(View, {
  name: 'Stack',
  flexDirection: 'column',
})

export const Text = styled(RNText, {
  name: 'Text',
  color: '$color',
  fontFamily: '$body',
})

export const Button = styled(TouchableOpacity, {
  name: 'Button',
  backgroundColor: '$background',
  paddingHorizontal: '$4',
  paddingVertical: '$3',
  borderRadius: '$4',
  alignItems: 'center',
  justifyContent: 'center',
  
  variants: {
    variant: {
      primary: {
        backgroundColor: '$blue10',
      },
      secondary: {
        backgroundColor: '$gray8',
      },
    },
    size: {
      small: {
        paddingHorizontal: '$3',
        paddingVertical: '$2',
      },
      large: {
        paddingHorizontal: '$5',
        paddingVertical: '$4',
      },
    },
  } as const,
})

export const Input = styled(TextInput, {
  name: 'Input',
  borderWidth: 1,
  borderColor: '$borderColor',
  borderRadius: '$4',
  paddingHorizontal: '$3',
  paddingVertical: '$3',
  fontSize: 16,
  color: '$color',
})
`;

  fs.writeFileSync(path.join(__dirname, 'src/components/styled.ts'), styledContent, 'utf8');
  console.log('✅ Created styled components');
}

// Main execution
console.log('🚀 Setting up complete UI components library...\n');

createTamaguiConfig();
updateMainIndex();
fixAllImports();
createStyledComponents();

console.log('\n✨ Library setup complete!');
console.log('\n📋 Next steps:');
console.log('1. Run: npm install');
console.log('2. Update component imports to use styled components');
console.log('3. Run: npm run build');
console.log('4. Test the library');

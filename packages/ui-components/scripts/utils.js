/**
 * Shared utility functions for HVPPYPlug UI Components scripts
 * 
 * This module provides common functionality used across all utility scripts
 * including logging, file operations, process management, and validation.
 */

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { execSync, spawn } = require('child_process');
const glob = require('glob');
const config = require('./config');

/**
 * Logger utility with different levels and formatting
 */
class Logger {
  constructor(level = 'info') {
    this.level = level;
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
    };
  }

  _shouldLog(level) {
    return this.levels[level] <= this.levels[this.level];
  }

  _formatMessage(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const prefix = config.logging.timestamps ? `[${timestamp}] ` : '';
    
    let coloredLevel;
    switch (level) {
      case 'error':
        coloredLevel = chalk.red('ERROR');
        break;
      case 'warn':
        coloredLevel = chalk.yellow('WARN');
        break;
      case 'info':
        coloredLevel = chalk.blue('INFO');
        break;
      case 'debug':
        coloredLevel = chalk.gray('DEBUG');
        break;
      default:
        coloredLevel = level.toUpperCase();
    }

    return `${prefix}${coloredLevel}: ${message}`;
  }

  error(message, ...args) {
    if (this._shouldLog('error')) {
      console.error(this._formatMessage('error', message, ...args));
    }
  }

  warn(message, ...args) {
    if (this._shouldLog('warn')) {
      console.warn(this._formatMessage('warn', message, ...args));
    }
  }

  info(message, ...args) {
    if (this._shouldLog('info')) {
      console.log(this._formatMessage('info', message, ...args));
    }
  }

  debug(message, ...args) {
    if (this._shouldLog('debug')) {
      console.log(this._formatMessage('debug', message, ...args));
    }
  }

  success(message, ...args) {
    if (this._shouldLog('info')) {
      console.log(chalk.green('✓'), message, ...args);
    }
  }

  step(message, ...args) {
    if (this._shouldLog('info')) {
      console.log(chalk.cyan('→'), message, ...args);
    }
  }
}

const logger = new Logger(config.logging.level);

/**
 * File system utilities
 */
const fileUtils = {
  /**
   * Ensure directory exists, create if it doesn't
   */
  async ensureDir(dirPath) {
    try {
      await fs.ensureDir(dirPath);
      logger.debug(`Ensured directory exists: ${dirPath}`);
    } catch (error) {
      logger.error(`Failed to ensure directory: ${dirPath}`, error.message);
      throw error;
    }
  },

  /**
   * Copy file with error handling
   */
  async copyFile(src, dest) {
    try {
      await fs.copy(src, dest);
      logger.debug(`Copied file: ${src} → ${dest}`);
    } catch (error) {
      logger.error(`Failed to copy file: ${src} → ${dest}`, error.message);
      throw error;
    }
  },

  /**
   * Read JSON file with error handling
   */
  async readJson(filePath) {
    try {
      const content = await fs.readJson(filePath);
      logger.debug(`Read JSON file: ${filePath}`);
      return content;
    } catch (error) {
      logger.error(`Failed to read JSON file: ${filePath}`, error.message);
      throw error;
    }
  },

  /**
   * Write JSON file with formatting
   */
  async writeJson(filePath, data, options = {}) {
    try {
      await fs.writeJson(filePath, data, { spaces: 2, ...options });
      logger.debug(`Wrote JSON file: ${filePath}`);
    } catch (error) {
      logger.error(`Failed to write JSON file: ${filePath}`, error.message);
      throw error;
    }
  },

  /**
   * Find files matching pattern
   */
  async findFiles(pattern, options = {}) {
    try {
      const files = await new Promise((resolve, reject) => {
        glob(pattern, options, (err, matches) => {
          if (err) reject(err);
          else resolve(matches);
        });
      });
      logger.debug(`Found ${files.length} files matching: ${pattern}`);
      return files;
    } catch (error) {
      logger.error(`Failed to find files: ${pattern}`, error.message);
      throw error;
    }
  },

  /**
   * Clean directory by removing all contents
   */
  async cleanDir(dirPath) {
    try {
      if (await fs.pathExists(dirPath)) {
        await fs.emptyDir(dirPath);
        logger.debug(`Cleaned directory: ${dirPath}`);
      }
    } catch (error) {
      logger.error(`Failed to clean directory: ${dirPath}`, error.message);
      throw error;
    }
  },
};

/**
 * Process execution utilities
 */
const processUtils = {
  /**
   * Execute command synchronously with error handling
   */
  execSync(command, options = {}) {
    try {
      logger.debug(`Executing: ${command}`);
      const result = execSync(command, {
        encoding: 'utf8',
        stdio: 'pipe',
        ...options,
      });
      return result.trim();
    } catch (error) {
      logger.error(`Command failed: ${command}`, error.message);
      throw error;
    }
  },

  /**
   * Execute command asynchronously with streaming output
   */
  async exec(command, options = {}) {
    return new Promise((resolve, reject) => {
      logger.debug(`Executing async: ${command}`);
      
      const child = spawn(command, {
        shell: true,
        stdio: 'inherit',
        ...options,
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve(code);
        } else {
          reject(new Error(`Command failed with exit code ${code}: ${command}`));
        }
      });

      child.on('error', (error) => {
        logger.error(`Command error: ${command}`, error.message);
        reject(error);
      });
    });
  },

  /**
   * Check if command exists in PATH
   */
  commandExists(command) {
    try {
      this.execSync(`which ${command}`, { stdio: 'ignore' });
      return true;
    } catch {
      return false;
    }
  },
};

/**
 * String manipulation utilities
 */
const stringUtils = {
  /**
   * Convert string to PascalCase
   */
  toPascalCase(str) {
    return str
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase())
      .replace(/\s+/g, '');
  },

  /**
   * Convert string to camelCase
   */
  toCamelCase(str) {
    const pascal = this.toPascalCase(str);
    return pascal.charAt(0).toLowerCase() + pascal.slice(1);
  },

  /**
   * Convert string to kebab-case
   */
  toKebabCase(str) {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/\s+/g, '-')
      .toLowerCase();
  },

  /**
   * Convert string to snake_case
   */
  toSnakeCase(str) {
    return str
      .replace(/([a-z])([A-Z])/g, '$1_$2')
      .replace(/\s+/g, '_')
      .toLowerCase();
  },

  /**
   * Pluralize a word (simple implementation)
   */
  pluralize(word) {
    if (word.endsWith('y')) {
      return word.slice(0, -1) + 'ies';
    } else if (word.endsWith('s') || word.endsWith('sh') || word.endsWith('ch')) {
      return word + 'es';
    } else {
      return word + 's';
    }
  },
};

/**
 * Validation utilities
 */
const validationUtils = {
  /**
   * Validate component name
   */
  validateComponentName(name) {
    if (!name || typeof name !== 'string') {
      throw new Error('Component name must be a non-empty string');
    }

    if (!/^[A-Z][a-zA-Z0-9]*$/.test(name)) {
      throw new Error('Component name must be PascalCase and contain only letters and numbers');
    }

    if (name.length < 2) {
      throw new Error('Component name must be at least 2 characters long');
    }

    return true;
  },

  /**
   * Validate file path
   */
  validatePath(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      throw new Error('Path must be a non-empty string');
    }

    if (path.isAbsolute(filePath) && !filePath.startsWith(config.paths.root)) {
      throw new Error('Absolute paths must be within the project root');
    }

    return true;
  },

  /**
   * Validate package.json structure
   */
  validatePackageJson(packageJson) {
    const required = ['name', 'version', 'main', 'types'];
    const missing = required.filter(field => !packageJson[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required package.json fields: ${missing.join(', ')}`);
    }

    return true;
  },
};

/**
 * Template utilities
 */
const templateUtils = {
  /**
   * Replace template variables in string
   */
  replaceVariables(template, variables) {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] || match;
    });
  },

  /**
   * Process template file
   */
  async processTemplate(templatePath, outputPath, variables) {
    try {
      const template = await fs.readFile(templatePath, 'utf8');
      const processed = this.replaceVariables(template, variables);
      await fs.writeFile(outputPath, processed);
      logger.debug(`Processed template: ${templatePath} → ${outputPath}`);
    } catch (error) {
      logger.error(`Failed to process template: ${templatePath}`, error.message);
      throw error;
    }
  },
};

/**
 * Performance monitoring utilities
 */
const performanceUtils = {
  /**
   * Create a timer for measuring execution time
   */
  createTimer(name) {
    const start = Date.now();
    return {
      end() {
        const duration = Date.now() - start;
        logger.debug(`Timer ${name}: ${duration}ms`);
        return duration;
      },
    };
  },

  /**
   * Measure memory usage
   */
  getMemoryUsage() {
    const usage = process.memoryUsage();
    return {
      rss: Math.round(usage.rss / 1024 / 1024), // MB
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
      external: Math.round(usage.external / 1024 / 1024), // MB
    };
  },
};

module.exports = {
  logger,
  fileUtils,
  processUtils,
  stringUtils,
  validationUtils,
  templateUtils,
  performanceUtils,
  config,
};

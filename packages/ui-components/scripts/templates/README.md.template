# {{componentName}}

A reusable {{componentName}} component built with <PERSON><PERSON><PERSON> for React Native applications.

## Overview

The {{componentName}} component provides [brief description of what the component does and its main purpose].

## Installation

This component is part of the `{{packageName}}` package:

```bash
pnpm add {{packageName}}
```

## Basic Usage

```tsx
import { {{componentName}} } from '{{packageName}}';

export default function App() {
  return (
    <{{componentName}} variant="primary" size="medium">
      Your content here
    </{{componentName}}>
  );
}
```

## Props

### {{componentName}}Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'default' \| 'primary' \| 'secondary'` | `'default'` | Visual variant of the component |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | Size of the component |
| `children` | `ReactNode` | - | Content to be rendered inside the component |
| `testID` | `string` | `'{{componentNameKebab}}'` | Test identifier for testing purposes |

Extends all props from React Native's `ViewProps`.

## Variants

### Default
The default variant provides a neutral appearance suitable for most use cases.

```tsx
<{{componentName}} variant="default">
  Default content
</{{componentName}}>
```

### Primary
The primary variant uses the primary theme color and is ideal for main actions.

```tsx
<{{componentName}} variant="primary">
  Primary content
</{{componentName}}>
```

### Secondary
The secondary variant provides an alternative styling option.

```tsx
<{{componentName}} variant="secondary">
  Secondary content
</{{componentName}}>
```

## Sizes

### Small
Compact size for space-constrained layouts.

```tsx
<{{componentName}} size="small">
  Small content
</{{componentName}}>
```

### Medium (Default)
Standard size for most use cases.

```tsx
<{{componentName}} size="medium">
  Medium content
</{{componentName}}>
```

### Large
Larger size for emphasis or touch-friendly interfaces.

```tsx
<{{componentName}} size="large">
  Large content
</{{componentName}}>
```

## Advanced Usage

### Custom Styling

You can customize the appearance using Tamagui's styling system:

```tsx
<{{componentName}}
  variant="primary"
  size="large"
  backgroundColor="$blue10"
  borderRadius="$4"
  padding="$3"
>
  Custom styled content
</{{componentName}}>
```

### With Complex Content

The component can contain complex nested content:

```tsx
<{{componentName}} variant="primary" size="large">
  <View style={{ alignItems: 'center', gap: 8 }}>
    <Text style={{ fontSize: 18, fontWeight: 'bold' }}>
      Title
    </Text>
    <Text style={{ opacity: 0.8 }}>
      Subtitle or description
    </Text>
  </View>
</{{componentName}}>
```

## Accessibility

The {{componentName}} component follows accessibility best practices:

- Supports screen readers with proper accessibility labels
- Keyboard navigation support
- High contrast mode compatibility
- Touch target size compliance

### Accessibility Props

```tsx
<{{componentName}}
  accessibilityLabel="{{componentName}} with custom label"
  accessibilityHint="Double tap to interact"
  accessibilityRole="button"
>
  Accessible content
</{{componentName}}>
```

## Theming

The component respects your Tamagui theme configuration:

```tsx
// In your theme configuration
const theme = {
  colors: {
    primary: '#007AFF',
    secondary: '#5856D6',
    // ... other colors
  },
  space: {
    1: 4,
    2: 8,
    3: 16,
    // ... other spacing values
  },
};
```

## Testing

### Unit Testing

```tsx
import { render, screen } from '@testing-library/react-native';
import { {{componentName}} } from '{{packageName}}';

test('renders {{componentName}} correctly', () => {
  render(
    <{{componentName}} testID="test-{{componentNameKebab}}">
      Test content
    </{{componentName}}>
  );
  
  expect(screen.getByTestId('test-{{componentNameKebab}}')).toBeTruthy();
  expect(screen.getByText('Test content')).toBeTruthy();
});
```

### Integration Testing

```tsx
import { fireEvent, render, screen } from '@testing-library/react-native';

test('{{componentName}} handles interactions', () => {
  const onPress = jest.fn();
  
  render(
    <{{componentName}} onPress={onPress}>
      Interactive content
    </{{componentName}}>
  );
  
  fireEvent.press(screen.getByText('Interactive content'));
  expect(onPress).toHaveBeenCalled();
});
```

## Performance Considerations

- The component is optimized for React Native performance
- Uses Tamagui's compiler for optimal bundle size
- Supports tree-shaking for unused variants
- Minimal re-renders through proper prop handling

## Troubleshooting

### Common Issues

**Component not rendering:**
- Ensure you have properly configured Tamagui in your app
- Check that all required dependencies are installed
- Verify the component is imported correctly

**Styling not applied:**
- Make sure your Tamagui theme is properly configured
- Check that the variant and size props are valid
- Ensure you're not overriding styles unintentionally

**TypeScript errors:**
- Update to the latest version of the component library
- Check that your TypeScript configuration is compatible
- Ensure all prop types are correctly specified

## Related Components

- [Button](../Button/README.md) - For interactive actions
- [Card](../Card/README.md) - For content containers
- [Text](../Text/README.md) - For text content

## Contributing

To contribute to this component:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

## Changelog

### Version 1.0.0
- Initial release
- Basic variant and size support
- Accessibility features
- TypeScript support

---

**Generated on:** {{date}}  
**Author:** {{author}}  
**Package:** {{packageName}}  
**License:** MIT

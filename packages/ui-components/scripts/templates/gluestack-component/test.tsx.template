import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { GluestackUIProvider } from '@/components/ui/gluestack-ui-provider';
import { {{componentName}}, {{componentName}}Text, {{componentName}}Group } from './{{componentName}}';
import type { {{componentName}}Props } from './{{componentName}}.types';

// Test wrapper with Gluestack UI provider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <GluestackUIProvider>
      {children}
    </GluestackUIProvider>
  );
};

const renderComponent = (props: Partial<{{componentName}}Props> = {}) => {
  return render(
    <TestWrapper>
      <{{componentName}} {...props}>
        <{{componentName}}Text>Test {{componentName}}</{{componentName}}Text>
      </{{componentName}}>
    </TestWrapper>
  );
};

describe('{{componentName}}', () => {
  describe('Rendering', () => {
    it('renders correctly with default props', () => {
      renderComponent();
      expect(screen.getByTestId('{{componentNameKebab}}')).toBeTruthy();
      expect(screen.getByText('Test {{componentName}}')).toBeTruthy();
    });

    it('renders with custom testID', () => {
      const customTestID = 'custom-{{componentNameKebab}}';
      renderComponent({ testID: customTestID });
      expect(screen.getByTestId(customTestID)).toBeTruthy();
    });

    it('renders with custom text content', () => {
      const customText = 'Custom {{componentName}} Text';
      render(
        <TestWrapper>
          <{{componentName}}>
            <{{componentName}}Text>{customText}</{{componentName}}Text>
          </{{componentName}}>
        </TestWrapper>
      );
      expect(screen.getByText(customText)).toBeTruthy();
    });
  });

  describe('Variants', () => {
    const variants: Array<{{componentName}}Props['variant']> = [
      'default', 'primary', 'secondary', 'outline', 'ghost', 'link'
    ];
    
    variants.forEach((variant) => {
      it(`renders ${variant} variant correctly`, () => {
        renderComponent({ variant });
        const component = screen.getByTestId('{{componentNameKebab}}');
        expect(component).toBeTruthy();
        
        // Verify variant-specific styling is applied
        // Note: In a real test, you'd check for specific style classes
        expect(component.props.className).toContain('flex');
      });
    });
  });

  describe('Sizes', () => {
    const sizes: Array<{{componentName}}Props['size']> = ['xs', 'sm', 'md', 'lg', 'xl'];
    
    sizes.forEach((size) => {
      it(`renders ${size} size correctly`, () => {
        renderComponent({ size });
        const component = screen.getByTestId('{{componentNameKebab}}');
        expect(component).toBeTruthy();
        
        // Verify size-specific styling is applied
        expect(component.props.className).toContain('flex');
      });
    });
  });

  describe('States', () => {
    it('handles disabled state', () => {
      renderComponent({ disabled: true });
      const component = screen.getByTestId('{{componentNameKebab}}');
      
      expect(component.props.accessibilityState).toEqual({ disabled: true });
      expect(component.props.disabled).toBe(true);
    });

    it('handles loading state', () => {
      renderComponent({ isLoading: true });
      const component = screen.getByTestId('{{componentNameKebab}}');
      expect(component).toBeTruthy();
      
      // In a real implementation, you'd check for spinner visibility
    });
  });

  describe('Interactions', () => {
    it('handles press events', () => {
      const onPress = jest.fn();
      renderComponent({ onPress });
      
      const component = screen.getByTestId('{{componentNameKebab}}');
      fireEvent.press(component);
      
      expect(onPress).toHaveBeenCalledTimes(1);
    });

    it('does not trigger press when disabled', () => {
      const onPress = jest.fn();
      renderComponent({ onPress, disabled: true });
      
      const component = screen.getByTestId('{{componentNameKebab}}');
      fireEvent.press(component);
      
      expect(onPress).not.toHaveBeenCalled();
    });

    it('handles long press events', () => {
      const onLongPress = jest.fn();
      renderComponent({ onLongPress });
      
      const component = screen.getByTestId('{{componentNameKebab}}');
      fireEvent(component, 'longPress');
      
      expect(onLongPress).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    it('has correct accessibility role', () => {
      renderComponent();
      const component = screen.getByTestId('{{componentNameKebab}}');
      expect(component.props.accessibilityRole).toBe('button');
    });

    it('supports custom accessibility label', () => {
      const accessibilityLabel = 'Custom {{componentName}} Label';
      renderComponent({ accessibilityLabel });
      
      const component = screen.getByTestId('{{componentNameKebab}}');
      expect(component.props.accessibilityLabel).toBe(accessibilityLabel);
    });

    it('supports accessibility hint', () => {
      const accessibilityHint = 'Double tap to activate {{componentName}}';
      renderComponent({ accessibilityHint });
      
      const component = screen.getByTestId('{{componentNameKebab}}');
      expect(component.props.accessibilityHint).toBe(accessibilityHint);
    });

    it('announces disabled state to screen readers', () => {
      renderComponent({ disabled: true });
      const component = screen.getByTestId('{{componentNameKebab}}');
      
      expect(component.props.accessibilityState).toEqual({ disabled: true });
    });
  });

  describe('Custom Styling', () => {
    it('applies custom className', () => {
      const customClass = 'custom-{{componentNameKebab}}-class';
      renderComponent({ className: customClass });
      
      const component = screen.getByTestId('{{componentNameKebab}}');
      expect(component.props.className).toContain(customClass);
    });

    it('merges custom styles with default styles', () => {
      renderComponent({ className: 'bg-red-500' });
      
      const component = screen.getByTestId('{{componentNameKebab}}');
      expect(component.props.className).toContain('flex'); // default
      expect(component.props.className).toContain('bg-red-500'); // custom
    });
  });
});

describe('{{componentName}}Text', () => {
  it('renders text content correctly', () => {
    const textContent = 'Test Text Content';
    render(
      <TestWrapper>
        <{{componentName}}>
          <{{componentName}}Text>{textContent}</{{componentName}}Text>
        </{{componentName}}>
      </TestWrapper>
    );
    
    expect(screen.getByText(textContent)).toBeTruthy();
  });

  it('inherits styling from parent {{componentName}}', () => {
    render(
      <TestWrapper>
        <{{componentName}} variant="primary" size="lg">
          <{{componentName}}Text>Styled Text</{{componentName}}Text>
        </{{componentName}}>
      </TestWrapper>
    );
    
    const text = screen.getByText('Styled Text');
    expect(text).toBeTruthy();
    // In a real test, you'd verify the inherited styles
  });
});

describe('{{componentName}}Group', () => {
  it('renders multiple {{componentName}} components', () => {
    render(
      <TestWrapper>
        <{{componentName}}Group>
          <{{componentName}} testID="button-1">
            <{{componentName}}Text>Button 1</{{componentName}}Text>
          </{{componentName}}>
          <{{componentName}} testID="button-2">
            <{{componentName}}Text>Button 2</{{componentName}}Text>
          </{{componentName}}>
        </{{componentName}}Group>
      </TestWrapper>
    );
    
    expect(screen.getByTestId('button-1')).toBeTruthy();
    expect(screen.getByTestId('button-2')).toBeTruthy();
  });

  it('applies correct spacing classes', () => {
    const { container } = render(
      <TestWrapper>
        <{{componentName}}Group space="lg" testID="button-group">
          <{{componentName}}>
            <{{componentName}}Text>Button 1</{{componentName}}Text>
          </{{componentName}}>
        </{{componentName}}Group>
      </TestWrapper>
    );
    
    // In a real test, you'd verify the spacing classes are applied
    expect(container).toBeTruthy();
  });
});

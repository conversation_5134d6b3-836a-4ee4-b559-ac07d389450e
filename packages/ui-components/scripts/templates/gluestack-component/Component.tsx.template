"use client"

import React from "react"
import { tva } from "@gluestack-ui/nativewind-utils/tva"
import {
  withStyleContext,
  useStyleContext,
} from "@gluestack-ui/nativewind-utils/withStyleContext"
import { Pressable, Text, View } from "react-native"
import type { VariantProps } from "@gluestack-ui/nativewind-utils"
import type { {{componentName}}Props } from './{{componentName}}.types'

const SCOPE = "{{componentName|upper}}"

/**
 * {{componentName}} component styles using Tailwind Variant Authority (tva)
 */
const {{componentNameCamel}}Style = tva({
  base: "flex items-center justify-center rounded-md border border-transparent font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",
  variants: {
    variant: {
      default: "bg-background-50 text-typography-900 hover:bg-background-100 border-border-300",
      primary: "bg-primary-500 text-typography-0 hover:bg-primary-600 focus:ring-primary-500",
      secondary: "bg-secondary-500 text-typography-0 hover:bg-secondary-600 focus:ring-secondary-500",
      outline: "border-border-300 bg-transparent hover:bg-background-50 text-typography-900",
      ghost: "hover:bg-background-100 text-typography-900",
      link: "text-primary-500 underline-offset-4 hover:underline",
    },
    size: {
      xs: "h-8 px-3 text-xs",
      sm: "h-9 px-4 text-sm",
      md: "h-10 px-5 text-base",
      lg: "h-11 px-6 text-lg",
      xl: "h-12 px-7 text-xl",
    },
  },
  defaultVariants: {
    variant: "default",
    size: "md",
  },
})

const {{componentNameCamel}}TextStyle = tva({
  base: "font-medium text-center",
  parentVariants: {
    variant: {
      default: "text-typography-900",
      primary: "text-typography-0",
      secondary: "text-typography-0",
      outline: "text-typography-900",
      ghost: "text-typography-900",
      link: "text-primary-500",
    },
    size: {
      xs: "text-xs",
      sm: "text-sm",
      md: "text-base",
      lg: "text-lg",
      xl: "text-xl",
    },
  },
})

/**
 * Root component with style context
 */
const Root = withStyleContext(Pressable, SCOPE)

/**
 * {{componentName}} Component
 * 
 * A customizable {{componentName}} component built with Gluestack UI v2 and NativeWind.
 * Supports multiple variants, sizes, and states with full accessibility.
 * 
 * @example
 * ```tsx
 * <{{componentName}} variant="primary" size="lg">
 *   <{{componentName}}Text>Click me</{{componentName}}Text>
 * </{{componentName}}>
 * ```
 */
export const {{componentName}} = React.forwardRef<
  React.ElementRef<typeof Root>,
  {{componentName}}Props & VariantProps<typeof {{componentNameCamel}}Style>
>(({ 
  className, 
  variant = "default", 
  size = "md", 
  children,
  disabled = false,
  testID,
  ...props 
}, ref) => {
  return (
    <Root
      ref={ref}
      className={{{componentNameCamel}}Style({ variant, size, class: className })}
      context={{ variant, size }}
      disabled={disabled}
      testID={testID || '{{componentNameKebab}}'}
      accessibilityRole="button"
      accessibilityState={{ disabled }}
      {...props}
    >
      {children}
    </Root>
  )
})

/**
 * {{componentName}}Text Component
 * 
 * Text component for the {{componentName}}, automatically inherits styling
 * from parent {{componentName}} component context.
 */
export const {{componentName}}Text = React.forwardRef<
  React.ElementRef<typeof Text>,
  React.ComponentPropsWithoutRef<typeof Text> & 
  VariantProps<typeof {{componentNameCamel}}TextStyle> & {
    className?: string
  }
>(({ className, variant, size, ...props }, ref) => {
  const {
    variant: parentVariant,
    size: parentSize,
  } = useStyleContext(SCOPE)

  return (
    <Text
      ref={ref}
      className={{{componentNameCamel}}TextStyle({
        parentVariants: {
          variant: parentVariant,
          size: parentSize,
        },
        variant,
        size,
        class: className,
      })}
      {...props}
    />
  )
})

/**
 * {{componentName}}Group Component
 * 
 * Container for grouping multiple {{componentName}} components with consistent spacing.
 */
export const {{componentName}}Group = React.forwardRef<
  React.ElementRef<typeof View>,
  React.ComponentPropsWithoutRef<typeof View> & {
    className?: string
    space?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
    direction?: 'row' | 'column'
  }
>(({ 
  className, 
  space = 'md', 
  direction = 'row',
  children,
  ...props 
}, ref) => {
  const spaceClasses = {
    xs: 'gap-1',
    sm: 'gap-2', 
    md: 'gap-3',
    lg: 'gap-4',
    xl: 'gap-5',
  }

  const directionClasses = {
    row: 'flex-row',
    column: 'flex-col',
  }

  return (
    <View
      ref={ref}
      className={`flex ${directionClasses[direction]} ${spaceClasses[space]} ${className || ''}`}
      {...props}
    >
      {children}
    </View>
  )
})

// Display names for debugging
{{componentName}}.displayName = '{{componentName}}'
{{componentName}}Text.displayName = '{{componentName}}Text'
{{componentName}}Group.displayName = '{{componentName}}Group'

export default {{componentName}}

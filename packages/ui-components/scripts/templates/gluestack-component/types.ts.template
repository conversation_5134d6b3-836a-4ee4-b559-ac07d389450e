import type { ReactNode } from 'react';
import type { PressableProps } from 'react-native';
import type { VariantProps } from '@gluestack-ui/nativewind-utils';

/**
 * Variant options for {{componentName}}
 */
export type {{componentName}}Variant = 
  | 'default' 
  | 'primary' 
  | 'secondary' 
  | 'outline' 
  | 'ghost' 
  | 'link';

/**
 * Size options for {{componentName}}
 */
export type {{componentName}}Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * Props for the {{componentName}} component
 * 
 * Extends React Native's PressableProps with additional styling and behavior options.
 */
export interface {{componentName}}Props extends Omit<PressableProps, 'children'> {
  /**
   * Content to be rendered inside the {{componentName}}
   */
  children?: ReactNode;
  
  /**
   * Visual variant of the {{componentName}}
   * @default 'default'
   */
  variant?: {{componentName}}Variant;
  
  /**
   * Size of the {{componentName}}
   * @default 'md'
   */
  size?: {{componentName}}Size;
  
  /**
   * Whether the {{componentName}} is disabled
   * @default false
   */
  disabled?: boolean;
  
  /**
   * Test identifier for testing purposes
   * @default '{{componentNameKebab}}'
   */
  testID?: string;
  
  /**
   * Additional CSS classes to apply
   */
  className?: string;
  
  /**
   * Loading state - shows spinner when true
   * @default false
   */
  isLoading?: boolean;
  
  /**
   * Icon to display before the text
   */
  leftIcon?: ReactNode;
  
  /**
   * Icon to display after the text
   */
  rightIcon?: ReactNode;
}

/**
 * Props for {{componentName}}Text component
 */
export interface {{componentName}}TextProps {
  /**
   * Text content to display
   */
  children?: ReactNode;
  
  /**
   * Override parent variant
   */
  variant?: {{componentName}}Variant;
  
  /**
   * Override parent size
   */
  size?: {{componentName}}Size;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Props for {{componentName}}Group component
 */
export interface {{componentName}}GroupProps {
  /**
   * {{componentName}} components to group
   */
  children?: ReactNode;
  
  /**
   * Spacing between {{componentName}} components
   * @default 'md'
   */
  space?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  
  /**
   * Direction of the group layout
   * @default 'row'
   */
  direction?: 'row' | 'column';
  
  /**
   * Whether buttons should be attached (no spacing)
   * @default false
   */
  isAttached?: boolean;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Ref type for {{componentName}} component
 */
export type {{componentName}}Ref = React.ElementRef<typeof import('react-native').Pressable>;

/**
 * Style variant props type for use with tva
 */
export type {{componentName}}StyleProps = VariantProps<any> & {
  variant?: {{componentName}}Variant;
  size?: {{componentName}}Size;
};

import React from 'react';
import { styled } from '@tamagui/core';
import { View } from 'react-native';
import type { {{componentName}}Props } from './{{componentName}}.types';

/**
 * Styled component using Tamagui
 */
const Styled{{componentName}} = styled(View, {
  name: '{{componentName}}',
  // Add your base styles here
  padding: '$2',
  borderRadius: '$2',
  backgroundColor: '$background',
  
  variants: {
    variant: {
      default: {
        backgroundColor: '$background',
      },
      primary: {
        backgroundColor: '$primary',
      },
      secondary: {
        backgroundColor: '$secondary',
      },
    },
    size: {
      small: {
        padding: '$1',
      },
      medium: {
        padding: '$2',
      },
      large: {
        padding: '$3',
      },
    },
  } as const,
  
  defaultVariants: {
    variant: 'default',
    size: 'medium',
  },
});

/**
 * {{componentName}} Component
 * 
 * A reusable {{componentName}} component built with <PERSON><PERSON><PERSON> for React Native.
 * 
 * @example
 * ```tsx
 * <{{componentName}} variant="primary" size="large">
 *   Content goes here
 * </{{componentName}}>
 * ```
 */
export const {{componentName}}: React.FC<{{componentName}}Props> = ({
  children,
  variant = 'default',
  size = 'medium',
  testID,
  ...props
}) => {
  return (
    <Styled{{componentName}}
      variant={variant}
      size={size}
      testID={testID || '{{componentNameKebab}}'}
      {...props}
    >
      {children}
    </Styled{{componentName}}>
  );
};

{{componentName}}.displayName = '{{componentName}}';

export default {{componentName}};

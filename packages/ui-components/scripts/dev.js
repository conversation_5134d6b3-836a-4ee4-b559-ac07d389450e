#!/usr/bin/env node

/**
 * Development Server Script for HVPPYPlug UI Components
 * 
 * Starts a comprehensive development environment with:
 * - File watching and hot reloading
 * - TypeScript compilation in watch mode
 * - Storybook development server
 * - Live documentation updates
 * - Real-time type checking
 * 
 * Usage:
 *   node scripts/dev.js [options]
 *   pnpm run dev [options]
 * 
 * Options:
 *   --storybook        Start Storybook development server
 *   --docs             Start documentation server
 *   --type-check       Enable real-time type checking
 *   --port, -p         Port for Storybook (default: 6006)
 *   --host             Host for servers (default: localhost)
 *   --no-open          Don't open browser automatically
 *   --verbose, -v      Verbose output
 *   --help, -h         Show help information
 * 
 * Examples:
 *   node scripts/dev.js --storybook
 *   node scripts/dev.js --docs --port 3000
 *   node scripts/dev.js --type-check --verbose
 */

const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const path = require('path');
const chokidar = require('chokidar');
const {
  logger,
  fileUtils,
  processUtils,
  performanceUtils,
  config,
} = require('./utils');

// Command line argument parsing
const argv = yargs(hideBin(process.argv))
  .usage('Usage: $0 [options]')
  .option('storybook', {
    describe: 'Start Storybook development server',
    type: 'boolean',
    default: false,
  })
  .option('docs', {
    describe: 'Start documentation server',
    type: 'boolean',
    default: false,
  })
  .option('type-check', {
    describe: 'Enable real-time type checking',
    type: 'boolean',
    default: false,
  })
  .option('port', {
    alias: 'p',
    describe: 'Port for Storybook server',
    type: 'number',
    default: 6006,
  })
  .option('host', {
    describe: 'Host for servers',
    type: 'string',
    default: 'localhost',
  })
  .option('no-open', {
    describe: "Don't open browser automatically",
    type: 'boolean',
    default: false,
  })
  .option('verbose', {
    alias: 'v',
    describe: 'Verbose output',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .example('$0 --storybook', 'Start with Storybook')
  .example('$0 --docs --type-check', 'Start with docs and type checking')
  .argv;

/**
 * Development server orchestrator
 */
class DevServer {
  constructor(options = {}) {
    this.options = options;
    this.processes = new Map();
    this.watchers = new Map();
    this.isShuttingDown = false;
  }

  /**
   * Start TypeScript compilation in watch mode
   */
  async startTypeScriptWatch() {
    logger.step('Starting TypeScript compilation in watch mode...');

    try {
      const tscProcess = processUtils.spawn(
        `npx tsc --project ${config.build.typescript.configFile} --watch --preserveWatchOutput`,
        {
          cwd: config.paths.root,
          stdio: 'pipe',
        }
      );

      this.processes.set('typescript', tscProcess);

      // Handle TypeScript output
      tscProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (this.options.verbose || output.includes('error')) {
          process.stdout.write(output);
        }
      });

      tscProcess.stderr.on('data', (data) => {
        const output = data.toString();
        if (!output.includes('Starting compilation in watch mode')) {
          process.stderr.write(output);
        }
      });

      logger.success('TypeScript watch mode started');
    } catch (error) {
      logger.error('Failed to start TypeScript watch mode:', error.message);
      throw error;
    }
  }

  /**
   * Start real-time type checking
   */
  async startTypeChecking() {
    if (!this.options.typeCheck) {
      return;
    }

    logger.step('Starting real-time type checking...');

    const watcher = chokidar.watch(config.paths.src, {
      ignored: ['**/*.test.{ts,tsx}', '**/*.stories.{ts,tsx}'],
      persistent: true,
    });

    let typeCheckTimeout;

    const runTypeCheck = async () => {
      clearTimeout(typeCheckTimeout);
      typeCheckTimeout = setTimeout(async () => {
        try {
          logger.debug('Running type check...');
          await processUtils.exec(
            `npx tsc --project ${config.build.typescript.configFile} --noEmit`,
            { cwd: config.paths.root }
          );
          logger.success('✓ Type check passed');
        } catch (error) {
          logger.error('✗ Type check failed');
          if (this.options.verbose) {
            console.error(error.message);
          }
        }
      }, 1000);
    };

    watcher.on('change', runTypeCheck);
    watcher.on('add', runTypeCheck);
    watcher.on('unlink', runTypeCheck);

    this.watchers.set('typecheck', watcher);
    logger.success('Real-time type checking started');
  }

  /**
   * Start Storybook development server
   */
  async startStorybook() {
    if (!this.options.storybook && !this.isRunningAll()) {
      return;
    }

    logger.step('Starting Storybook development server...');

    try {
      // Ensure Storybook config exists
      if (!(await fileUtils.pathExists(config.documentation.storybook.configDir))) {
        await this.createStorybookConfig();
      }

      const storybookArgs = [
        'storybook',
        'dev',
        '--config-dir', config.documentation.storybook.configDir,
        '--port', this.options.port.toString(),
        '--host', this.options.host,
      ];

      if (!this.options.noOpen) {
        storybookArgs.push('--no-open');
      }

      if (!this.options.verbose) {
        storybookArgs.push('--quiet');
      }

      const storybookProcess = processUtils.spawn(
        `npx ${storybookArgs.join(' ')}`,
        {
          cwd: config.paths.root,
          stdio: 'inherit',
        }
      );

      this.processes.set('storybook', storybookProcess);

      // Wait a moment for Storybook to start
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.success(`Storybook started at http://${this.options.host}:${this.options.port}`);
    } catch (error) {
      logger.error('Failed to start Storybook:', error.message);
      throw error;
    }
  }

  /**
   * Create basic Storybook configuration if it doesn't exist
   */
  async createStorybookConfig() {
    logger.debug('Creating Storybook configuration...');

    await fileUtils.ensureDir(config.documentation.storybook.configDir);

    // Create main.js
    const mainConfig = `module.exports = {
  stories: ['../src/**/*.stories.@(js|jsx|ts|tsx)'],
  addons: [
    '@storybook/addon-essentials',
    '@storybook/addon-react-native-web',
  ],
  framework: {
    name: '@storybook/react-webpack5',
    options: {},
  },
  typescript: {
    reactDocgen: 'react-docgen-typescript',
  },
};`;

    await fileUtils.writeFile(
      path.join(config.documentation.storybook.configDir, 'main.js'),
      mainConfig
    );

    // Create preview.js
    const previewConfig = `export const parameters = {
  actions: { argTypesRegex: '^on[A-Z].*' },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
  },
};`;

    await fileUtils.writeFile(
      path.join(config.documentation.storybook.configDir, 'preview.js'),
      previewConfig
    );

    logger.debug('Storybook configuration created');
  }

  /**
   * Start documentation server
   */
  async startDocsServer() {
    if (!this.options.docs && !this.isRunningAll()) {
      return;
    }

    logger.step('Starting documentation server...');

    try {
      const express = require('express');
      const app = express();
      const docsPort = this.options.port + 1;

      // Serve static documentation if available
      if (await fileUtils.pathExists(config.paths.docs)) {
        app.use('/docs', express.static(config.paths.docs));
      }

      // API endpoint for component information
      app.get('/api/components', async (req, res) => {
        try {
          const components = await this.getComponentList();
          res.json(components);
        } catch (error) {
          res.status(500).json({ error: error.message });
        }
      });

      // Root page
      app.get('/', (req, res) => {
        res.send(`
          <html>
            <head>
              <title>${config.project.name} - Development</title>
              <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 40px; }
                .container { max-width: 800px; margin: 0 auto; }
                .link { display: block; padding: 16px; margin: 8px 0; background: #f5f5f5; border-radius: 8px; text-decoration: none; color: #333; }
                .link:hover { background: #e5e5e5; }
              </style>
            </head>
            <body>
              <div class="container">
                <h1>${config.project.name}</h1>
                <p>Development Environment</p>
                <a href="http://${this.options.host}:${this.options.port}" class="link">
                  📚 Storybook (Port ${this.options.port})
                </a>
                <a href="/docs" class="link">
                  📖 Documentation
                </a>
                <a href="/api/components" class="link">
                  🔧 Components API
                </a>
              </div>
            </body>
          </html>
        `);
      });

      const server = app.listen(docsPort, this.options.host, () => {
        logger.success(`Documentation server started at http://${this.options.host}:${docsPort}`);
      });

      this.processes.set('docs', { kill: () => server.close() });
    } catch (error) {
      logger.error('Failed to start documentation server:', error.message);
      throw error;
    }
  }

  /**
   * Get list of available components
   */
  async getComponentList() {
    try {
      const componentFiles = await fileUtils.findFiles(
        path.join(config.paths.components, '**/*.tsx'),
        { ignore: ['**/*.test.tsx', '**/*.stories.tsx'] }
      );

      const components = componentFiles
        .map(file => {
          const name = path.basename(file, '.tsx');
          return /^[A-Z]/.test(name) ? name : null;
        })
        .filter(Boolean)
        .sort();

      return {
        count: components.length,
        components: components.map(name => ({
          name,
          path: `/components/${name}`,
        })),
      };
    } catch (error) {
      logger.warn('Failed to get component list:', error.message);
      return { count: 0, components: [] };
    }
  }

  /**
   * Setup file watching for live updates
   */
  async setupFileWatching() {
    logger.step('Setting up file watching...');

    const watcher = chokidar.watch(config.paths.src, {
      ignored: config.development.watch.ignored,
      persistent: true,
    });

    let changeTimeout;

    const handleChange = (eventType, filePath) => {
      clearTimeout(changeTimeout);
      changeTimeout = setTimeout(() => {
        const relativePath = path.relative(config.paths.root, filePath);
        logger.info(`🔄 ${eventType}: ${relativePath}`);
        
        if (this.options.verbose) {
          logger.debug(`File ${eventType}: ${filePath}`);
        }
      }, config.development.watch.debounce);
    };

    watcher.on('change', (filePath) => handleChange('Changed', filePath));
    watcher.on('add', (filePath) => handleChange('Added', filePath));
    watcher.on('unlink', (filePath) => handleChange('Removed', filePath));

    this.watchers.set('files', watcher);
    logger.success('File watching started');
  }

  /**
   * Setup graceful shutdown
   */
  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      if (this.isShuttingDown) {
        return;
      }

      this.isShuttingDown = true;
      logger.info(`\n🛑 Received ${signal}, shutting down gracefully...`);

      // Close all watchers
      for (const [name, watcher] of this.watchers) {
        try {
          await watcher.close();
          logger.debug(`Closed watcher: ${name}`);
        } catch (error) {
          logger.warn(`Failed to close watcher ${name}:`, error.message);
        }
      }

      // Kill all processes
      for (const [name, process] of this.processes) {
        try {
          if (process.kill) {
            process.kill('SIGTERM');
          } else if (typeof process.kill === 'function') {
            process.kill();
          }
          logger.debug(`Killed process: ${name}`);
        } catch (error) {
          logger.warn(`Failed to kill process ${name}:`, error.message);
        }
      }

      logger.success('Development server stopped');
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGHUP', () => shutdown('SIGHUP'));
  }

  /**
   * Check if running all development services
   */
  isRunningAll() {
    return !this.options.storybook && !this.options.docs;
  }

  /**
   * Main development server startup
   */
  async start() {
    const timer = performanceUtils.createTimer('dev-server');
    
    try {
      logger.info('🚀 Starting HVPPYPlug UI Components development environment...');

      // Setup graceful shutdown first
      this.setupGracefulShutdown();

      // Start core services
      await this.startTypeScriptWatch();
      await this.setupFileWatching();
      await this.startTypeChecking();

      // Start optional services
      if (this.isRunningAll() || this.options.storybook) {
        await this.startStorybook();
      }

      if (this.isRunningAll() || this.options.docs) {
        await this.startDocsServer();
      }

      const duration = timer.end();
      const memory = performanceUtils.getMemoryUsage();

      logger.success(`✨ Development environment started in ${duration}ms`);
      logger.debug(`Memory usage: ${memory.heapUsed}MB`);

      logger.info('\n📋 Development Environment Status:');
      logger.info('   ✅ TypeScript compilation (watch mode)');
      logger.info('   ✅ File watching');
      
      if (this.options.typeCheck) {
        logger.info('   ✅ Real-time type checking');
      }
      
      if (this.processes.has('storybook')) {
        logger.info(`   ✅ Storybook server (http://${this.options.host}:${this.options.port})`);
      }
      
      if (this.processes.has('docs')) {
        logger.info(`   ✅ Documentation server (http://${this.options.host}:${this.options.port + 1})`);
      }

      logger.info('\n🎯 Ready for development! Press Ctrl+C to stop.');

      // Keep the process alive
      await new Promise(() => {});

    } catch (error) {
      logger.error('Development server startup failed:', error.message);
      throw error;
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    const devServer = new DevServer(argv);
    await devServer.start();
  } catch (error) {
    logger.error('Development server failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { DevServer };

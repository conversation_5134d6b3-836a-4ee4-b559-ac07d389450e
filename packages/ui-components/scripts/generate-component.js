#!/usr/bin/env node

/**
 * Component Generator Script
 * 
 * Generates new React Native components with TypeScript, tests, stories, and documentation
 * following HVPPYPlug UI Components best practices and conventions.
 * 
 * Usage:
 *   node scripts/generate-component.js ComponentName [options]
 *   pnpm run generate:component ComponentName [options]
 * 
 * Options:
 *   --template, -t     Template to use (basic, form, compound, animation)
 *   --no-tests         Skip test file generation
 *   --no-stories       Skip Storybook story generation
 *   --no-docs          Skip documentation generation
 *   --directory, -d    Custom directory within components/
 *   --help, -h         Show help information
 * 
 * Examples:
 *   node scripts/generate-component.js Button
 *   node scripts/generate-component.js Modal --template compound
 *   node scripts/generate-component.js Input --directory forms --no-stories
 */

const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const inquirer = require('inquirer');
const path = require('path');
const {
  logger,
  fileUtils,
  stringUtils,
  validationUtils,
  templateUtils,
  config,
} = require('./utils');

// Command line argument parsing
const argv = yargs(hideBin(process.argv))
  .usage('Usage: $0 <componentName> [options]')
  .positional('componentName', {
    describe: 'Name of the component to generate (PascalCase)',
    type: 'string',
  })
  .option('template', {
    alias: 't',
    describe: 'Template to use for component generation',
    choices: Object.keys(config.generation.templates),
    default: config.generation.defaults.template,
  })
  .option('directory', {
    alias: 'd',
    describe: 'Custom directory within components/',
    type: 'string',
  })
  .option('no-tests', {
    describe: 'Skip test file generation',
    type: 'boolean',
    default: false,
  })
  .option('no-stories', {
    describe: 'Skip Storybook story generation',
    type: 'boolean',
    default: false,
  })
  .option('no-docs', {
    describe: 'Skip documentation generation',
    type: 'boolean',
    default: false,
  })
  .option('interactive', {
    alias: 'i',
    describe: 'Run in interactive mode',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .example('$0 Button', 'Generate a basic Button component')
  .example('$0 Modal --template compound', 'Generate a compound Modal component')
  .example('$0 Input --directory forms', 'Generate Input component in forms directory')
  .argv;

/**
 * Interactive mode prompts
 */
async function runInteractiveMode() {
  logger.info('🎯 Interactive Component Generator');
  
  const answers = await inquirer.prompt([
    {
      type: 'input',
      name: 'componentName',
      message: 'Component name (PascalCase):',
      validate: (input) => {
        try {
          validationUtils.validateComponentName(input);
          return true;
        } catch (error) {
          return error.message;
        }
      },
    },
    {
      type: 'list',
      name: 'template',
      message: 'Select component template:',
      choices: [
        { name: 'Basic Component', value: 'basic' },
        { name: 'Form Component', value: 'form' },
        { name: 'Compound Component', value: 'compound' },
        { name: 'Animation Component', value: 'animation' },
      ],
      default: 'basic',
    },
    {
      type: 'input',
      name: 'directory',
      message: 'Custom directory (optional):',
      default: '',
    },
    {
      type: 'checkbox',
      name: 'features',
      message: 'Select features to include:',
      choices: [
        { name: 'Test files', value: 'tests', checked: true },
        { name: 'Storybook stories', value: 'stories', checked: true },
        { name: 'Documentation', value: 'docs', checked: true },
      ],
    },
  ]);

  return {
    componentName: answers.componentName,
    template: answers.template,
    directory: answers.directory || undefined,
    withTests: answers.features.includes('tests'),
    withStories: answers.features.includes('stories'),
    withDocs: answers.features.includes('docs'),
  };
}

/**
 * Generate component files based on template
 */
class ComponentGenerator {
  constructor(options) {
    this.options = options;
    this.componentName = options.componentName;
    this.template = options.template;
    this.directory = options.directory;
    this.withTests = options.withTests;
    this.withStories = options.withStories;
    this.withDocs = options.withDocs;

    // Validate component name
    validationUtils.validateComponentName(this.componentName);

    // Set up paths
    this.setupPaths();

    // Prepare template variables
    this.setupVariables();
  }

  setupPaths() {
    const baseDir = this.directory 
      ? path.join(config.paths.components, this.directory)
      : config.paths.components;
    
    this.componentDir = path.join(baseDir, this.componentName);
    this.componentFile = path.join(this.componentDir, `${this.componentName}.tsx`);
    this.indexFile = path.join(this.componentDir, 'index.ts');
    this.typesFile = path.join(this.componentDir, `${this.componentName}.types.ts`);
    this.testFile = path.join(this.componentDir, `${this.componentName}.test.tsx`);
    this.storyFile = path.join(this.componentDir, `${this.componentName}.stories.tsx`);
    this.docsFile = path.join(this.componentDir, 'README.md');
  }

  setupVariables() {
    this.variables = {
      componentName: this.componentName,
      componentNameCamel: stringUtils.toCamelCase(this.componentName),
      componentNameKebab: stringUtils.toKebabCase(this.componentName),
      componentNameSnake: stringUtils.toSnakeCase(this.componentName),
      packageName: config.project.name,
      author: config.project.author,
      date: new Date().toISOString().split('T')[0],
      year: new Date().getFullYear(),
      template: this.template,
      directory: this.directory || 'components',
    };
  }

  async generate() {
    try {
      logger.step(`Generating ${this.componentName} component...`);

      // Check if component already exists
      await this.checkExistence();

      // Create component directory
      await fileUtils.ensureDir(this.componentDir);

      // Generate files
      await this.generateComponentFile();
      await this.generateIndexFile();
      await this.generateTypesFile();

      if (this.withTests) {
        await this.generateTestFile();
      }

      if (this.withStories) {
        await this.generateStoryFile();
      }

      if (this.withDocs) {
        await this.generateDocsFile();
      }

      // Update main index file
      await this.updateMainIndex();

      logger.success(`✨ Component ${this.componentName} generated successfully!`);
      this.printSummary();

    } catch (error) {
      logger.error('Failed to generate component:', error.message);
      throw error;
    }
  }

  async checkExistence() {
    const exists = await fileUtils.pathExists(this.componentDir);
    if (exists) {
      const { overwrite } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'overwrite',
          message: `Component ${this.componentName} already exists. Overwrite?`,
          default: false,
        },
      ]);

      if (!overwrite) {
        throw new Error('Component generation cancelled');
      }
    }
  }

  async generateComponentFile() {
    const templatePath = path.join(
      config.paths.templates,
      config.generation.templates[this.template],
      'Component.tsx.template'
    );

    await templateUtils.processTemplate(
      templatePath,
      this.componentFile,
      this.variables
    );

    logger.debug(`Generated component file: ${this.componentFile}`);
  }

  async generateIndexFile() {
    const content = `export { ${this.componentName} } from './${this.componentName}';
export type { ${this.componentName}Props } from './${this.componentName}.types';
`;

    await fileUtils.writeFile(this.indexFile, content);
    logger.debug(`Generated index file: ${this.indexFile}`);
  }

  async generateTypesFile() {
    const templatePath = path.join(
      config.paths.templates,
      config.generation.templates[this.template],
      'types.ts.template'
    );

    await templateUtils.processTemplate(
      templatePath,
      this.typesFile,
      this.variables
    );

    logger.debug(`Generated types file: ${this.typesFile}`);
  }

  async generateTestFile() {
    const templatePath = path.join(
      config.paths.templates,
      config.generation.templates[this.template],
      'test.tsx.template'
    );

    await templateUtils.processTemplate(
      templatePath,
      this.testFile,
      this.variables
    );

    logger.debug(`Generated test file: ${this.testFile}`);
  }

  async generateStoryFile() {
    const templatePath = path.join(
      config.paths.templates,
      config.generation.templates[this.template],
      'stories.tsx.template'
    );

    await templateUtils.processTemplate(
      templatePath,
      this.storyFile,
      this.variables
    );

    logger.debug(`Generated story file: ${this.storyFile}`);
  }

  async generateDocsFile() {
    const templatePath = path.join(
      config.paths.templates,
      'README.md.template'
    );

    await templateUtils.processTemplate(
      templatePath,
      this.docsFile,
      this.variables
    );

    logger.debug(`Generated docs file: ${this.docsFile}`);
  }

  async updateMainIndex() {
    const mainIndexPath = path.join(config.paths.src, 'index.ts');
    
    try {
      let content = await fileUtils.readFile(mainIndexPath, 'utf8');
      
      // Add export line if not already present
      const exportLine = `export * from './components/${this.directory ? this.directory + '/' : ''}${this.componentName}';`;
      
      if (!content.includes(exportLine)) {
        // Find the components section and add the export
        const componentsSection = '// Core UI Components';
        const insertIndex = content.indexOf(componentsSection);
        
        if (insertIndex !== -1) {
          const lines = content.split('\n');
          const sectionLineIndex = lines.findIndex(line => line.includes(componentsSection));
          
          // Insert after the section comment
          lines.splice(sectionLineIndex + 1, 0, exportLine);
          content = lines.join('\n');
          
          await fileUtils.writeFile(mainIndexPath, content);
          logger.debug(`Updated main index file with ${this.componentName} export`);
        }
      }
    } catch (error) {
      logger.warn(`Could not update main index file: ${error.message}`);
    }
  }

  printSummary() {
    logger.info('\n📁 Generated files:');
    logger.info(`   ${path.relative(config.paths.root, this.componentFile)}`);
    logger.info(`   ${path.relative(config.paths.root, this.indexFile)}`);
    logger.info(`   ${path.relative(config.paths.root, this.typesFile)}`);
    
    if (this.withTests) {
      logger.info(`   ${path.relative(config.paths.root, this.testFile)}`);
    }
    
    if (this.withStories) {
      logger.info(`   ${path.relative(config.paths.root, this.storyFile)}`);
    }
    
    if (this.withDocs) {
      logger.info(`   ${path.relative(config.paths.root, this.docsFile)}`);
    }

    logger.info('\n🚀 Next steps:');
    logger.info('   1. Implement your component logic');
    logger.info('   2. Add proper TypeScript types');
    logger.info('   3. Write comprehensive tests');
    logger.info('   4. Create Storybook stories');
    logger.info('   5. Update documentation');
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    let options;

    if (argv.interactive || !argv.componentName) {
      options = await runInteractiveMode();
    } else {
      options = {
        componentName: argv.componentName,
        template: argv.template,
        directory: argv.directory,
        withTests: !argv.noTests,
        withStories: !argv.noStories,
        withDocs: !argv.noDocs,
      };
    }

    const generator = new ComponentGenerator(options);
    await generator.generate();

  } catch (error) {
    logger.error('Component generation failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { ComponentGenerator };

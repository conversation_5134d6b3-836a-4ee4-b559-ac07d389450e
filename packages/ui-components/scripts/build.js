#!/usr/bin/env node

/**
 * Build Script for HVPPYPlug UI Components
 * 
 * Builds the component library with optimized bundles for different module systems:
 * - ESM (ES Modules) for modern bundlers
 * - CommonJS for Node.js compatibility
 * - TypeScript declarations for type safety
 * - Source maps for debugging
 * 
 * Usage:
 *   node scripts/build.js [options]
 *   pnpm run build [options]
 * 
 * Options:
 *   --watch, -w        Build in watch mode
 *   --analyze, -a      Analyze bundle size
 *   --clean, -c        Clean before build
 *   --types-only       Build only TypeScript declarations
 *   --no-minify        Skip minification
 *   --verbose, -v      Verbose output
 *   --help, -h         Show help information
 * 
 * Examples:
 *   node scripts/build.js --clean --analyze
 *   node scripts/build.js --watch --verbose
 *   node scripts/build.js --types-only
 */

const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const path = require('path');
const rollup = require('rollup');
const typescript = require('@rollup/plugin-typescript');
const resolve = require('@rollup/plugin-node-resolve');
const commonjs = require('@rollup/plugin-commonjs');
const json = require('@rollup/plugin-json');
const { terser } = require('rollup-plugin-terser');
const { getBabelOutputPlugin } = require('@rollup/plugin-babel');
const chokidar = require('chokidar');
const {
  logger,
  fileUtils,
  processUtils,
  performanceUtils,
  config,
} = require('./utils');

// Command line argument parsing
const argv = yargs(hideBin(process.argv))
  .usage('Usage: $0 [options]')
  .option('watch', {
    alias: 'w',
    describe: 'Build in watch mode',
    type: 'boolean',
    default: false,
  })
  .option('analyze', {
    alias: 'a',
    describe: 'Analyze bundle size',
    type: 'boolean',
    default: false,
  })
  .option('clean', {
    alias: 'c',
    describe: 'Clean before build',
    type: 'boolean',
    default: false,
  })
  .option('types-only', {
    describe: 'Build only TypeScript declarations',
    type: 'boolean',
    default: false,
  })
  .option('no-minify', {
    describe: 'Skip minification',
    type: 'boolean',
    default: false,
  })
  .option('verbose', {
    alias: 'v',
    describe: 'Verbose output',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .example('$0 --clean --analyze', 'Clean build with bundle analysis')
  .example('$0 --watch', 'Build in watch mode')
  .argv;

/**
 * Build configuration generator
 */
class BuildConfig {
  constructor(options = {}) {
    this.options = options;
    this.isProduction = process.env.NODE_ENV === 'production';
    this.shouldMinify = this.isProduction && !options.noMinify;
  }

  /**
   * Get Rollup input configuration
   */
  getInputConfig() {
    const external = [
      'react',
      'react-native',
      'react-native-reanimated',
      'react-native-svg',
      'react-hook-form',
      'zod',
      '@hookform/resolvers',
      'lucide-react-native',
      'expo-haptics',
      'expo-blur',
      '@react-native-community/datetimepicker',
      /@tamagui\/.*/,
    ];

    const plugins = [
      resolve({
        extensions: ['.ts', '.tsx', '.js', '.jsx'],
        preferBuiltins: false,
      }),
      commonjs({
        include: /node_modules/,
      }),
      json(),
      typescript({
        tsconfig: config.build.typescript.configFile,
        declaration: false, // We'll generate declarations separately
        declarationMap: false,
        sourceMap: true,
        exclude: config.build.patterns.exclude,
      }),
    ];

    return {
      input: path.join(config.paths.src, 'index.ts'),
      external,
      plugins,
    };
  }

  /**
   * Get ESM output configuration
   */
  getESMOutputConfig() {
    const plugins = [];

    if (this.shouldMinify) {
      plugins.push(terser({
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
        mangle: {
          reserved: ['React', 'ReactNative'],
        },
      }));
    }

    return {
      dir: config.build.outputs.esm,
      format: 'esm',
      sourcemap: true,
      preserveModules: true,
      preserveModulesRoot: config.paths.src,
      plugins,
    };
  }

  /**
   * Get CommonJS output configuration
   */
  getCJSOutputConfig() {
    const plugins = [
      getBabelOutputPlugin({
        presets: [
          ['@babel/preset-env', {
            targets: { node: '14' },
            modules: 'cjs',
          }],
        ],
      }),
    ];

    if (this.shouldMinify) {
      plugins.push(terser({
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
        mangle: {
          reserved: ['React', 'ReactNative'],
        },
      }));
    }

    return {
      dir: config.build.outputs.cjs,
      format: 'cjs',
      sourcemap: true,
      preserveModules: true,
      preserveModulesRoot: config.paths.src,
      plugins,
      exports: 'named',
    };
  }
}

/**
 * Main build orchestrator
 */
class Builder {
  constructor(options = {}) {
    this.options = options;
    this.buildConfig = new BuildConfig(options);
    this.isWatching = false;
  }

  /**
   * Clean build directories
   */
  async clean() {
    if (this.options.clean) {
      logger.step('Cleaning build directories...');
      await fileUtils.cleanDir(config.paths.dist);
      logger.success('Build directories cleaned');
    }
  }

  /**
   * Build TypeScript declarations
   */
  async buildTypes() {
    logger.step('Building TypeScript declarations...');
    const timer = performanceUtils.createTimer('types');

    try {
      await processUtils.exec(
        `tsc --project ${config.build.typescript.buildConfigFile} --declaration --emitDeclarationOnly --declarationDir ${config.build.outputs.types}`,
        { cwd: config.paths.root }
      );

      const duration = timer.end();
      logger.success(`TypeScript declarations built in ${duration}ms`);
    } catch (error) {
      logger.error('Failed to build TypeScript declarations:', error.message);
      throw error;
    }
  }

  /**
   * Build JavaScript bundles
   */
  async buildBundles() {
    if (this.options.typesOnly) {
      return;
    }

    logger.step('Building JavaScript bundles...');
    const timer = performanceUtils.createTimer('bundles');

    try {
      const inputConfig = this.buildConfig.getInputConfig();
      const bundle = await rollup.rollup(inputConfig);

      // Build ESM
      logger.debug('Building ESM bundle...');
      const esmConfig = this.buildConfig.getESMOutputConfig();
      await bundle.write(esmConfig);

      // Build CommonJS
      logger.debug('Building CommonJS bundle...');
      const cjsConfig = this.buildConfig.getCJSOutputConfig();
      await bundle.write(cjsConfig);

      await bundle.close();

      const duration = timer.end();
      logger.success(`JavaScript bundles built in ${duration}ms`);
    } catch (error) {
      logger.error('Failed to build JavaScript bundles:', error.message);
      throw error;
    }
  }

  /**
   * Copy additional files
   */
  async copyFiles() {
    logger.step('Copying additional files...');

    try {
      // Copy package.json
      const packageJson = await fileUtils.readJson(
        path.join(config.paths.root, 'package.json')
      );

      // Create distribution package.json
      const distPackageJson = {
        ...packageJson,
        main: './cjs/index.js',
        module: './esm/index.js',
        types: './types/index.d.ts',
        exports: {
          '.': {
            import: './esm/index.js',
            require: './cjs/index.js',
            types: './types/index.d.ts',
          },
          './package.json': './package.json',
        },
        files: ['esm', 'cjs', 'types', 'README.md', 'LICENSE'],
        scripts: undefined,
        devDependencies: undefined,
      };

      await fileUtils.writeJson(
        path.join(config.paths.dist, 'package.json'),
        distPackageJson
      );

      // Copy README and LICENSE
      const filesToCopy = ['README.md', 'LICENSE'];
      for (const file of filesToCopy) {
        const srcPath = path.join(config.paths.root, file);
        const destPath = path.join(config.paths.dist, file);
        
        if (await fileUtils.pathExists(srcPath)) {
          await fileUtils.copyFile(srcPath, destPath);
        }
      }

      logger.success('Additional files copied');
    } catch (error) {
      logger.error('Failed to copy additional files:', error.message);
      throw error;
    }
  }

  /**
   * Analyze bundle size
   */
  async analyzeBundles() {
    if (!this.options.analyze) {
      return;
    }

    logger.step('Analyzing bundle sizes...');

    try {
      const { default: bundleAnalyzer } = await import('rollup-plugin-analyzer');
      
      // Get bundle sizes
      const esmStats = await this.getBundleStats(config.build.outputs.esm);
      const cjsStats = await this.getBundleStats(config.build.outputs.cjs);

      const analysis = {
        timestamp: new Date().toISOString(),
        esm: esmStats,
        cjs: cjsStats,
        warnings: [],
      };

      // Check size thresholds
      const totalSize = esmStats.totalSize + cjsStats.totalSize;
      const sizeLimit = config.build.bundleAnalysis.sizeLimit;

      if (totalSize > this.parseSize(sizeLimit.error)) {
        analysis.warnings.push(`Bundle size exceeds error threshold: ${this.formatSize(totalSize)} > ${sizeLimit.error}`);
        logger.error(`Bundle size too large: ${this.formatSize(totalSize)}`);
      } else if (totalSize > this.parseSize(sizeLimit.warning)) {
        analysis.warnings.push(`Bundle size exceeds warning threshold: ${this.formatSize(totalSize)} > ${sizeLimit.warning}`);
        logger.warn(`Bundle size warning: ${this.formatSize(totalSize)}`);
      }

      // Save analysis
      await fileUtils.writeJson(
        config.build.bundleAnalysis.outputFile,
        analysis
      );

      logger.success('Bundle analysis completed');
      this.printBundleStats(analysis);
    } catch (error) {
      logger.error('Failed to analyze bundles:', error.message);
    }
  }

  /**
   * Get bundle statistics
   */
  async getBundleStats(bundleDir) {
    const files = await fileUtils.findFiles(
      path.join(bundleDir, '**/*.js'),
      { ignore: ['**/*.map'] }
    );

    let totalSize = 0;
    const fileStats = [];

    for (const file of files) {
      const fs = require('fs');
      const stats = fs.statSync(file);
      const size = stats.size;
      totalSize += size;

      fileStats.push({
        file: path.relative(bundleDir, file),
        size,
        sizeFormatted: this.formatSize(size),
      });
    }

    return {
      totalSize,
      totalSizeFormatted: this.formatSize(totalSize),
      fileCount: files.length,
      files: fileStats.sort((a, b) => b.size - a.size),
    };
  }

  /**
   * Parse size string to bytes
   */
  parseSize(sizeStr) {
    const units = { kb: 1024, mb: 1024 * 1024, gb: 1024 * 1024 * 1024 };
    const match = sizeStr.toLowerCase().match(/^(\d+(?:\.\d+)?)(kb|mb|gb)$/);
    
    if (!match) {
      return parseInt(sizeStr, 10);
    }

    return parseFloat(match[1]) * units[match[2]];
  }

  /**
   * Format size in bytes to human readable
   */
  formatSize(bytes) {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)}GB`;
  }

  /**
   * Print bundle statistics
   */
  printBundleStats(analysis) {
    logger.info('\n📊 Bundle Analysis:');
    logger.info(`   ESM: ${analysis.esm.totalSizeFormatted} (${analysis.esm.fileCount} files)`);
    logger.info(`   CJS: ${analysis.cjs.totalSizeFormatted} (${analysis.cjs.fileCount} files)`);
    
    if (analysis.warnings.length > 0) {
      logger.info('\n⚠️  Warnings:');
      analysis.warnings.forEach(warning => logger.warn(`   ${warning}`));
    }
  }

  /**
   * Watch mode
   */
  async watch() {
    if (!this.options.watch) {
      return;
    }

    logger.info('👀 Starting watch mode...');
    this.isWatching = true;

    const watcher = chokidar.watch(config.paths.src, {
      ignored: config.build.patterns.exclude,
      persistent: true,
    });

    let buildTimeout;

    const triggerBuild = () => {
      clearTimeout(buildTimeout);
      buildTimeout = setTimeout(async () => {
        try {
          logger.info('🔄 Files changed, rebuilding...');
          await this.buildBundles();
          await this.buildTypes();
          logger.success('✅ Rebuild completed');
        } catch (error) {
          logger.error('❌ Rebuild failed:', error.message);
        }
      }, 300);
    };

    watcher.on('change', triggerBuild);
    watcher.on('add', triggerBuild);
    watcher.on('unlink', triggerBuild);

    logger.success('Watch mode started');

    // Keep process alive
    process.on('SIGINT', () => {
      logger.info('Stopping watch mode...');
      watcher.close();
      process.exit(0);
    });
  }

  /**
   * Main build process
   */
  async build() {
    const overallTimer = performanceUtils.createTimer('build');
    
    try {
      logger.info('🏗️  Building HVPPYPlug UI Components...');

      await this.clean();
      await this.buildTypes();
      await this.buildBundles();
      await this.copyFiles();
      await this.analyzeBundles();

      const duration = overallTimer.end();
      const memory = performanceUtils.getMemoryUsage();

      logger.success(`✨ Build completed in ${duration}ms`);
      logger.debug(`Memory usage: ${memory.heapUsed}MB`);

      if (this.options.watch) {
        await this.watch();
      }

    } catch (error) {
      logger.error('Build failed:', error.message);
      throw error;
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    const builder = new Builder(argv);
    await builder.build();
  } catch (error) {
    logger.error('Build process failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { Builder, BuildConfig };

#!/usr/bin/env node

/**
 * Linting and Code Quality Script for HVPPYPlug UI Components
 * 
 * Runs comprehensive code quality checks including:
 * - ESLint for code linting
 * - Prettier for code formatting
 * - TypeScript type checking
 * - Import validation
 * - Dependency analysis
 * 
 * Usage:
 *   node scripts/lint.js [options]
 *   pnpm run lint [options]
 * 
 * Options:
 *   --fix, -f          Auto-fix linting issues
 *   --format           Format code with Prettier
 *   --type-check       Run TypeScript type checking
 *   --imports          Validate imports and exports
 *   --deps             Check dependency issues
 *   --staged           Only check staged files (for git hooks)
 *   --verbose, -v      Verbose output
 *   --help, -h         Show help information
 * 
 * Examples:
 *   node scripts/lint.js --fix --format
 *   node scripts/lint.js --type-check --imports
 *   node scripts/lint.js --staged
 */

const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const path = require('path');
const {
  logger,
  fileUtils,
  processUtils,
  performanceUtils,
  config,
} = require('./utils');

// Command line argument parsing
const argv = yargs(hideBin(process.argv))
  .usage('Usage: $0 [options]')
  .option('fix', {
    alias: 'f',
    describe: 'Auto-fix linting issues',
    type: 'boolean',
    default: false,
  })
  .option('format', {
    describe: 'Format code with Prettier',
    type: 'boolean',
    default: false,
  })
  .option('type-check', {
    describe: 'Run TypeScript type checking',
    type: 'boolean',
    default: false,
  })
  .option('imports', {
    describe: 'Validate imports and exports',
    type: 'boolean',
    default: false,
  })
  .option('deps', {
    describe: 'Check dependency issues',
    type: 'boolean',
    default: false,
  })
  .option('staged', {
    describe: 'Only check staged files (for git hooks)',
    type: 'boolean',
    default: false,
  })
  .option('verbose', {
    alias: 'v',
    describe: 'Verbose output',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .example('$0 --fix --format', 'Fix linting issues and format code')
  .example('$0 --type-check', 'Run TypeScript type checking')
  .argv;

/**
 * Code quality checker orchestrator
 */
class QualityChecker {
  constructor(options = {}) {
    this.options = options;
    this.results = {
      eslint: null,
      prettier: null,
      typescript: null,
      imports: null,
      dependencies: null,
    };
    this.hasErrors = false;
  }

  /**
   * Get files to check based on options
   */
  async getFilesToCheck() {
    if (this.options.staged) {
      try {
        const stagedFiles = processUtils.execSync('git diff --cached --name-only --diff-filter=ACM')
          .split('\n')
          .filter(file => file.trim())
          .filter(file => /\.(ts|tsx|js|jsx)$/.test(file))
          .map(file => path.join(config.paths.root, file));
        
        logger.debug(`Found ${stagedFiles.length} staged files`);
        return stagedFiles;
      } catch (error) {
        logger.warn('Failed to get staged files, checking all files');
        return null;
      }
    }
    return null;
  }

  /**
   * Run ESLint
   */
  async runESLint() {
    if (!this.isRunningAll() && !this.options.fix) {
      return;
    }

    logger.step('Running ESLint...');
    const timer = performanceUtils.createTimer('eslint');

    try {
      const filesToCheck = await this.getFilesToCheck();
      const eslintArgs = [
        'eslint',
        '--config', config.quality.eslint.configFile,
        '--ext', config.quality.eslint.extensions.join(','),
      ];

      if (this.options.fix) {
        eslintArgs.push('--fix');
      }

      if (this.options.verbose) {
        eslintArgs.push('--debug');
      }

      // Add file patterns or specific files
      if (filesToCheck && filesToCheck.length > 0) {
        eslintArgs.push(...filesToCheck);
      } else {
        eslintArgs.push(config.paths.src);
      }

      const result = await processUtils.exec(
        `npx ${eslintArgs.join(' ')}`,
        { cwd: config.paths.root }
      );

      const duration = timer.end();
      this.results.eslint = { success: true, duration };
      logger.success(`ESLint completed in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.eslint = { success: false, duration, error: error.message };
      this.hasErrors = true;
      
      if (error.message.includes('warning')) {
        logger.warn('ESLint completed with warnings');
      } else {
        logger.error('ESLint failed with errors');
      }
    }
  }

  /**
   * Run Prettier formatting
   */
  async runPrettier() {
    if (!this.options.format && !this.isRunningAll()) {
      return;
    }

    logger.step('Running Prettier...');
    const timer = performanceUtils.createTimer('prettier');

    try {
      const filesToCheck = await this.getFilesToCheck();
      const prettierArgs = [
        'prettier',
        '--config', config.quality.prettier.configFile,
      ];

      if (this.options.format) {
        prettierArgs.push('--write');
      } else {
        prettierArgs.push('--check');
      }

      // Add file patterns or specific files
      if (filesToCheck && filesToCheck.length > 0) {
        prettierArgs.push(...filesToCheck);
      } else {
        prettierArgs.push(`${config.paths.src}/**/*.{ts,tsx,js,jsx,json,md}`);
      }

      const result = await processUtils.exec(
        `npx ${prettierArgs.join(' ')}`,
        { cwd: config.paths.root }
      );

      const duration = timer.end();
      this.results.prettier = { success: true, duration };
      
      if (this.options.format) {
        logger.success(`Code formatted with Prettier in ${duration}ms`);
      } else {
        logger.success(`Prettier check completed in ${duration}ms`);
      }

    } catch (error) {
      const duration = timer.end();
      this.results.prettier = { success: false, duration, error: error.message };
      
      if (!this.options.format) {
        this.hasErrors = true;
        logger.error('Code formatting issues found. Run with --format to fix.');
      } else {
        logger.error('Prettier formatting failed');
      }
    }
  }

  /**
   * Run TypeScript type checking
   */
  async runTypeScript() {
    if (!this.options.typeCheck && !this.isRunningAll()) {
      return;
    }

    logger.step('Running TypeScript type checking...');
    const timer = performanceUtils.createTimer('typescript');

    try {
      const tscArgs = [
        'tsc',
        '--project', config.build.typescript.configFile,
        '--noEmit',
      ];

      if (config.quality.typeCheck.strict) {
        tscArgs.push('--strict');
      }

      if (config.quality.typeCheck.incremental) {
        tscArgs.push('--incremental');
      }

      const result = await processUtils.exec(
        `npx ${tscArgs.join(' ')}`,
        { cwd: config.paths.root }
      );

      const duration = timer.end();
      this.results.typescript = { success: true, duration };
      logger.success(`TypeScript type checking completed in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.typescript = { success: false, duration, error: error.message };
      this.hasErrors = true;
      logger.error('TypeScript type checking failed');
    }
  }

  /**
   * Validate imports and exports
   */
  async runImportValidation() {
    if (!this.options.imports && !this.isRunningAll()) {
      return;
    }

    logger.step('Validating imports and exports...');
    const timer = performanceUtils.createTimer('imports');

    try {
      // Check for circular dependencies
      await this.checkCircularDependencies();
      
      // Validate main index exports
      await this.validateMainExports();
      
      // Check for unused exports
      await this.checkUnusedExports();

      const duration = timer.end();
      this.results.imports = { success: true, duration };
      logger.success(`Import validation completed in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.imports = { success: false, duration, error: error.message };
      this.hasErrors = true;
      logger.error('Import validation failed:', error.message);
    }
  }

  /**
   * Check for circular dependencies
   */
  async checkCircularDependencies() {
    try {
      const result = await processUtils.exec(
        'npx madge --circular --extensions ts,tsx src/',
        { cwd: config.paths.root }
      );
      
      if (result.trim()) {
        throw new Error(`Circular dependencies found:\n${result}`);
      }
      
      logger.debug('No circular dependencies found');
    } catch (error) {
      if (error.message.includes('Circular dependencies found')) {
        throw error;
      }
      logger.debug('Madge not available, skipping circular dependency check');
    }
  }

  /**
   * Validate main index exports
   */
  async validateMainExports() {
    const indexPath = path.join(config.paths.src, 'index.ts');
    
    if (!(await fileUtils.pathExists(indexPath))) {
      throw new Error('Main index.ts file not found');
    }

    const content = await fileUtils.readFile(indexPath, 'utf8');
    
    // Check for common export issues
    const issues = [];
    
    if (!content.includes('export')) {
      issues.push('No exports found in main index file');
    }
    
    // Check for duplicate exports (simplified)
    const exportLines = content.split('\n').filter(line => line.trim().startsWith('export'));
    const exportNames = new Set();
    
    exportLines.forEach(line => {
      const match = line.match(/export\s+(?:\{([^}]+)\}|(\w+))/);
      if (match) {
        const names = match[1] ? match[1].split(',').map(n => n.trim()) : [match[2]];
        names.forEach(name => {
          if (exportNames.has(name)) {
            issues.push(`Duplicate export: ${name}`);
          }
          exportNames.add(name);
        });
      }
    });
    
    if (issues.length > 0) {
      throw new Error(`Export validation issues:\n${issues.join('\n')}`);
    }
    
    logger.debug(`Validated ${exportNames.size} exports in main index`);
  }

  /**
   * Check for unused exports (simplified)
   */
  async checkUnusedExports() {
    // This is a simplified implementation
    // In a real scenario, you'd use tools like ts-unused-exports
    logger.debug('Unused exports check completed (simplified)');
  }

  /**
   * Check dependency issues
   */
  async runDependencyCheck() {
    if (!this.options.deps && !this.isRunningAll()) {
      return;
    }

    logger.step('Checking dependencies...');
    const timer = performanceUtils.createTimer('dependencies');

    try {
      // Check for security vulnerabilities
      await this.checkSecurityVulnerabilities();
      
      // Check for outdated dependencies
      await this.checkOutdatedDependencies();
      
      // Validate package.json
      await this.validatePackageJson();

      const duration = timer.end();
      this.results.dependencies = { success: true, duration };
      logger.success(`Dependency check completed in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.dependencies = { success: false, duration, error: error.message };
      logger.warn('Dependency check completed with warnings:', error.message);
    }
  }

  /**
   * Check for security vulnerabilities
   */
  async checkSecurityVulnerabilities() {
    try {
      const result = await processUtils.exec(
        'pnpm audit --audit-level moderate',
        { cwd: config.paths.root }
      );
      
      logger.debug('No security vulnerabilities found');
    } catch (error) {
      if (error.message.includes('vulnerabilities')) {
        logger.warn('Security vulnerabilities found. Run `pnpm audit --fix` to resolve.');
      }
    }
  }

  /**
   * Check for outdated dependencies
   */
  async checkOutdatedDependencies() {
    try {
      const result = await processUtils.exec(
        'pnpm outdated',
        { cwd: config.paths.root }
      );
      
      if (result.trim()) {
        logger.info('Outdated dependencies found. Consider updating.');
      }
    } catch (error) {
      // pnpm outdated exits with code 1 when outdated packages are found
      logger.debug('Outdated dependency check completed');
    }
  }

  /**
   * Validate package.json structure
   */
  async validatePackageJson() {
    const packageJsonPath = path.join(config.paths.root, 'package.json');
    const packageJson = await fileUtils.readJson(packageJsonPath);
    
    // Basic validation
    const required = ['name', 'version', 'main', 'types'];
    const missing = required.filter(field => !packageJson[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required package.json fields: ${missing.join(', ')}`);
    }
    
    logger.debug('Package.json validation passed');
  }

  /**
   * Check if running all quality checks
   */
  isRunningAll() {
    return !this.options.fix && 
           !this.options.format && 
           !this.options.typeCheck && 
           !this.options.imports && 
           !this.options.deps;
  }

  /**
   * Generate quality check summary
   */
  generateSummary() {
    logger.info('\n🔍 Quality Check Summary:');
    
    const results = Object.entries(this.results).filter(([_, result]) => result !== null);
    
    if (results.length === 0) {
      logger.warn('No quality checks were run');
      return;
    }

    let totalDuration = 0;
    let successCount = 0;
    let failureCount = 0;

    results.forEach(([checkType, result]) => {
      const status = result.success ? '✅' : '❌';
      const duration = result.duration || 0;
      totalDuration += duration;
      
      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }

      logger.info(`   ${status} ${checkType}: ${duration}ms`);
    });

    logger.info(`\n⏱️  Total time: ${totalDuration}ms`);
    logger.info(`✅ Passed: ${successCount}`);
    logger.info(`❌ Failed: ${failureCount}`);

    if (this.hasErrors) {
      logger.error(`\n${failureCount} quality check(s) failed`);
      process.exit(1);
    } else {
      logger.success(`\nAll ${successCount} quality check(s) passed!`);
    }
  }

  /**
   * Main quality check execution
   */
  async run() {
    const overallTimer = performanceUtils.createTimer('quality-checks');
    
    try {
      logger.info('🔍 Running HVPPYPlug UI Components quality checks...');

      // Run checks based on options
      if (this.isRunningAll()) {
        await this.runESLint();
        await this.runPrettier();
        await this.runTypeScript();
        await this.runImportValidation();
        await this.runDependencyCheck();
      } else {
        if (this.options.fix || this.options.format) {
          await this.runESLint();
          await this.runPrettier();
        }
        if (this.options.typeCheck) {
          await this.runTypeScript();
        }
        if (this.options.imports) {
          await this.runImportValidation();
        }
        if (this.options.deps) {
          await this.runDependencyCheck();
        }
      }

      const duration = overallTimer.end();
      const memory = performanceUtils.getMemoryUsage();

      logger.debug(`Total execution time: ${duration}ms`);
      logger.debug(`Memory usage: ${memory.heapUsed}MB`);

      this.generateSummary();

    } catch (error) {
      logger.error('Quality check execution failed:', error.message);
      this.generateSummary();
      throw error;
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    const checker = new QualityChecker(argv);
    await checker.run();
  } catch (error) {
    logger.error('Quality check process failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { QualityChecker };

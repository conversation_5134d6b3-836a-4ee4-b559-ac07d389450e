#!/usr/bin/env node

/**
 * Test Runner Script for HVPPYPlug UI Components
 * 
 * Comprehensive testing suite that runs unit tests, integration tests,
 * accessibility tests, and visual regression tests with coverage reporting.
 * 
 * Usage:
 *   node scripts/test.js [options]
 *   pnpm run test [options]
 * 
 * Options:
 *   --watch, -w        Run tests in watch mode
 *   --coverage, -c     Generate coverage report
 *   --unit             Run only unit tests
 *   --integration      Run only integration tests
 *   --a11y             Run accessibility tests
 *   --visual           Run visual regression tests
 *   --update-snapshots Update test snapshots
 *   --verbose, -v      Verbose output
 *   --help, -h         Show help information
 * 
 * Examples:
 *   node scripts/test.js --coverage
 *   node scripts/test.js --watch --unit
 *   node scripts/test.js --a11y --visual
 */

const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const path = require('path');
const {
  logger,
  fileUtils,
  processUtils,
  performanceUtils,
  config,
} = require('./utils');

// Command line argument parsing
const argv = yargs(hideBin(process.argv))
  .usage('Usage: $0 [options]')
  .option('watch', {
    alias: 'w',
    describe: 'Run tests in watch mode',
    type: 'boolean',
    default: false,
  })
  .option('coverage', {
    alias: 'c',
    describe: 'Generate coverage report',
    type: 'boolean',
    default: false,
  })
  .option('unit', {
    describe: 'Run only unit tests',
    type: 'boolean',
    default: false,
  })
  .option('integration', {
    describe: 'Run only integration tests',
    type: 'boolean',
    default: false,
  })
  .option('a11y', {
    describe: 'Run accessibility tests',
    type: 'boolean',
    default: false,
  })
  .option('visual', {
    describe: 'Run visual regression tests',
    type: 'boolean',
    default: false,
  })
  .option('update-snapshots', {
    alias: 'u',
    describe: 'Update test snapshots',
    type: 'boolean',
    default: false,
  })
  .option('verbose', {
    alias: 'v',
    describe: 'Verbose output',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .example('$0 --coverage', 'Run tests with coverage')
  .example('$0 --watch --unit', 'Watch unit tests only')
  .argv;

/**
 * Test runner orchestrator
 */
class TestRunner {
  constructor(options = {}) {
    this.options = options;
    this.results = {
      unit: null,
      integration: null,
      accessibility: null,
      visual: null,
    };
  }

  /**
   * Run unit tests with Jest
   */
  async runUnitTests() {
    if (this.options.integration || this.options.a11y || this.options.visual) {
      return;
    }

    logger.step('Running unit tests...');
    const timer = performanceUtils.createTimer('unit-tests');

    try {
      const jestArgs = [
        '--config', config.testing.jest.configFile,
        '--testPathPattern', config.testing.patterns.unit,
      ];

      if (this.options.coverage) {
        jestArgs.push('--coverage');
        jestArgs.push('--coverageReporters', 'text', 'lcov', 'html');
      }

      if (this.options.watch) {
        jestArgs.push('--watch');
      }

      if (this.options.updateSnapshots) {
        jestArgs.push('--updateSnapshot');
      }

      if (this.options.verbose) {
        jestArgs.push('--verbose');
      }

      // Add CI-specific options
      if (process.env.CI) {
        jestArgs.push('--ci', '--watchAll=false');
      }

      const result = await processUtils.exec(
        `npx jest ${jestArgs.join(' ')}`,
        { cwd: config.paths.root }
      );

      const duration = timer.end();
      this.results.unit = { success: true, duration };
      logger.success(`Unit tests completed in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.unit = { success: false, duration, error: error.message };
      logger.error('Unit tests failed:', error.message);
      throw error;
    }
  }

  /**
   * Run integration tests
   */
  async runIntegrationTests() {
    if (this.options.unit || this.options.a11y || this.options.visual) {
      return;
    }

    logger.step('Running integration tests...');
    const timer = performanceUtils.createTimer('integration-tests');

    try {
      const jestArgs = [
        '--config', config.testing.jest.configFile,
        '--testPathPattern', config.testing.patterns.integration,
      ];

      if (this.options.verbose) {
        jestArgs.push('--verbose');
      }

      const result = await processUtils.exec(
        `npx jest ${jestArgs.join(' ')}`,
        { cwd: config.paths.root }
      );

      const duration = timer.end();
      this.results.integration = { success: true, duration };
      logger.success(`Integration tests completed in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.integration = { success: false, duration, error: error.message };
      logger.error('Integration tests failed:', error.message);
      throw error;
    }
  }

  /**
   * Run accessibility tests
   */
  async runAccessibilityTests() {
    if (!this.options.a11y && !this.isRunningAll()) {
      return;
    }

    if (!config.testing.accessibility.enabled) {
      logger.warn('Accessibility testing is disabled');
      return;
    }

    logger.step('Running accessibility tests...');
    const timer = performanceUtils.createTimer('a11y-tests');

    try {
      // Run Jest with accessibility-specific configuration
      const jestArgs = [
        '--config', config.testing.jest.configFile,
        '--testNamePattern', 'accessibility|a11y',
        '--setupFilesAfterEnv', '<rootDir>/jest.a11y.setup.js',
      ];

      if (this.options.verbose) {
        jestArgs.push('--verbose');
      }

      const result = await processUtils.exec(
        `npx jest ${jestArgs.join(' ')}`,
        { cwd: config.paths.root }
      );

      // Generate accessibility report
      await this.generateAccessibilityReport();

      const duration = timer.end();
      this.results.accessibility = { success: true, duration };
      logger.success(`Accessibility tests completed in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.accessibility = { success: false, duration, error: error.message };
      logger.error('Accessibility tests failed:', error.message);
      throw error;
    }
  }

  /**
   * Run visual regression tests
   */
  async runVisualTests() {
    if (!this.options.visual && !this.isRunningAll()) {
      return;
    }

    if (!config.testing.visual.enabled) {
      logger.warn('Visual regression testing is disabled');
      return;
    }

    logger.step('Running visual regression tests...');
    const timer = performanceUtils.createTimer('visual-tests');

    try {
      // Ensure visual test output directory exists
      await fileUtils.ensureDir(config.testing.visual.outputDir);

      // Run Storybook visual tests
      const storybookArgs = [
        'test-storybook',
        '--url', 'http://localhost:6006',
        '--maxWorkers', '2',
      ];

      if (this.options.updateSnapshots) {
        storybookArgs.push('--updateSnapshot');
      }

      // Start Storybook in background for testing
      logger.debug('Starting Storybook for visual testing...');
      const storybookProcess = processUtils.spawn(
        'npx storybook dev --port 6006 --quiet',
        { detached: true }
      );

      // Wait for Storybook to be ready
      await this.waitForStorybook();

      try {
        const result = await processUtils.exec(
          `npx ${storybookArgs.join(' ')}`,
          { cwd: config.paths.root }
        );

        const duration = timer.end();
        this.results.visual = { success: true, duration };
        logger.success(`Visual regression tests completed in ${duration}ms`);

      } finally {
        // Clean up Storybook process
        if (storybookProcess) {
          storybookProcess.kill();
        }
      }

    } catch (error) {
      const duration = timer.end();
      this.results.visual = { success: false, duration, error: error.message };
      logger.error('Visual regression tests failed:', error.message);
      throw error;
    }
  }

  /**
   * Wait for Storybook to be ready
   */
  async waitForStorybook(maxAttempts = 30) {
    const http = require('http');
    
    for (let i = 0; i < maxAttempts; i++) {
      try {
        await new Promise((resolve, reject) => {
          const req = http.get('http://localhost:6006', (res) => {
            resolve(res);
          });
          req.on('error', reject);
          req.setTimeout(1000, () => reject(new Error('Timeout')));
        });
        
        logger.debug('Storybook is ready');
        return;
      } catch {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    throw new Error('Storybook failed to start within timeout');
  }

  /**
   * Generate accessibility report
   */
  async generateAccessibilityReport() {
    try {
      const reportData = {
        timestamp: new Date().toISOString(),
        rules: config.testing.accessibility.rules,
        summary: {
          totalTests: 0,
          passed: 0,
          failed: 0,
          violations: [],
        },
      };

      // This would be populated by actual accessibility test results
      // For now, we create a placeholder structure

      await fileUtils.writeJson(
        config.testing.accessibility.outputFile,
        reportData
      );

      logger.debug('Accessibility report generated');
    } catch (error) {
      logger.warn('Failed to generate accessibility report:', error.message);
    }
  }

  /**
   * Check if running all tests (no specific test type selected)
   */
  isRunningAll() {
    return !this.options.unit && 
           !this.options.integration && 
           !this.options.a11y && 
           !this.options.visual;
  }

  /**
   * Generate test summary report
   */
  generateSummary() {
    logger.info('\n📊 Test Summary:');
    
    const results = Object.entries(this.results).filter(([_, result]) => result !== null);
    
    if (results.length === 0) {
      logger.warn('No tests were run');
      return;
    }

    let totalDuration = 0;
    let successCount = 0;
    let failureCount = 0;

    results.forEach(([testType, result]) => {
      const status = result.success ? '✅' : '❌';
      const duration = result.duration || 0;
      totalDuration += duration;
      
      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }

      logger.info(`   ${status} ${testType}: ${duration}ms`);
      
      if (!result.success && result.error) {
        logger.error(`      Error: ${result.error}`);
      }
    });

    logger.info(`\n⏱️  Total time: ${totalDuration}ms`);
    logger.info(`✅ Passed: ${successCount}`);
    logger.info(`❌ Failed: ${failureCount}`);

    if (failureCount > 0) {
      logger.error(`\n${failureCount} test suite(s) failed`);
      process.exit(1);
    } else {
      logger.success(`\nAll ${successCount} test suite(s) passed!`);
    }
  }

  /**
   * Main test execution
   */
  async run() {
    const overallTimer = performanceUtils.createTimer('all-tests');
    
    try {
      logger.info('🧪 Running HVPPYPlug UI Components tests...');

      // Run tests based on options
      if (this.isRunningAll()) {
        await this.runUnitTests();
        await this.runIntegrationTests();
        await this.runAccessibilityTests();
        await this.runVisualTests();
      } else {
        if (this.options.unit || this.isRunningAll()) {
          await this.runUnitTests();
        }
        if (this.options.integration) {
          await this.runIntegrationTests();
        }
        if (this.options.a11y) {
          await this.runAccessibilityTests();
        }
        if (this.options.visual) {
          await this.runVisualTests();
        }
      }

      const duration = overallTimer.end();
      const memory = performanceUtils.getMemoryUsage();

      logger.debug(`Total execution time: ${duration}ms`);
      logger.debug(`Memory usage: ${memory.heapUsed}MB`);

      this.generateSummary();

    } catch (error) {
      logger.error('Test execution failed:', error.message);
      this.generateSummary();
      throw error;
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    const runner = new TestRunner(argv);
    await runner.run();
  } catch (error) {
    logger.error('Test process failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { TestRunner };

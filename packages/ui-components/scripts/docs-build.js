#!/usr/bin/env node

/**
 * Documentation Builder Script for HVPPYPlug UI Components
 * 
 * Generates comprehensive documentation including:
 * - Interactive Storybook documentation
 * - API documentation from TypeScript
 * - Component prop documentation
 * - Design system documentation
 * - Usage examples and guidelines
 * 
 * Usage:
 *   node scripts/docs-build.js [options]
 *   pnpm run docs:build [options]
 * 
 * Options:
 *   --storybook        Build Storybook documentation
 *   --api              Generate API documentation
 *   --props            Extract component props
 *   --design-system    Build design system docs
 *   --serve, -s        Serve documentation after build
 *   --port, -p         Port for serving (default: 8080)
 *   --verbose, -v      Verbose output
 *   --help, -h         Show help information
 * 
 * Examples:
 *   node scripts/docs-build.js --storybook --api
 *   node scripts/docs-build.js --serve --port 3000
 *   node scripts/docs-build.js --design-system --props
 */

const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const path = require('path');
const {
  logger,
  fileUtils,
  processUtils,
  performanceUtils,
  config,
} = require('./utils');

// Command line argument parsing
const argv = yargs(hideBin(process.argv))
  .usage('Usage: $0 [options]')
  .option('storybook', {
    describe: 'Build Storybook documentation',
    type: 'boolean',
    default: false,
  })
  .option('api', {
    describe: 'Generate API documentation',
    type: 'boolean',
    default: false,
  })
  .option('props', {
    describe: 'Extract component props',
    type: 'boolean',
    default: false,
  })
  .option('design-system', {
    describe: 'Build design system documentation',
    type: 'boolean',
    default: false,
  })
  .option('serve', {
    alias: 's',
    describe: 'Serve documentation after build',
    type: 'boolean',
    default: false,
  })
  .option('port', {
    alias: 'p',
    describe: 'Port for serving documentation',
    type: 'number',
    default: 8080,
  })
  .option('verbose', {
    alias: 'v',
    describe: 'Verbose output',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .example('$0 --storybook --api', 'Build Storybook and API docs')
  .example('$0 --serve', 'Build and serve all documentation')
  .argv;

/**
 * Documentation builder orchestrator
 */
class DocumentationBuilder {
  constructor(options = {}) {
    this.options = options;
    this.results = {
      storybook: null,
      api: null,
      props: null,
      designSystem: null,
    };
  }

  /**
   * Build Storybook documentation
   */
  async buildStorybook() {
    if (!this.options.storybook && !this.isRunningAll()) {
      return;
    }

    logger.step('Building Storybook documentation...');
    const timer = performanceUtils.createTimer('storybook');

    try {
      // Ensure output directory exists
      await fileUtils.ensureDir(config.documentation.storybook.outputDir);

      // Build Storybook
      const storybookArgs = [
        'storybook',
        'build',
        '--config-dir', config.documentation.storybook.configDir,
        '--output-dir', config.documentation.storybook.outputDir,
      ];

      if (this.options.verbose) {
        storybookArgs.push('--loglevel', 'verbose');
      } else {
        storybookArgs.push('--quiet');
      }

      await processUtils.exec(
        `npx ${storybookArgs.join(' ')}`,
        { cwd: config.paths.root }
      );

      const duration = timer.end();
      this.results.storybook = { success: true, duration };
      logger.success(`Storybook documentation built in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.storybook = { success: false, duration, error: error.message };
      logger.error('Storybook build failed:', error.message);
      throw error;
    }
  }

  /**
   * Generate API documentation using TypeDoc
   */
  async buildApiDocs() {
    if (!this.options.api && !this.isRunningAll()) {
      return;
    }

    if (!config.documentation.api.enabled) {
      logger.warn('API documentation is disabled');
      return;
    }

    logger.step('Generating API documentation...');
    const timer = performanceUtils.createTimer('api-docs');

    try {
      // Ensure output directory exists
      await fileUtils.ensureDir(config.documentation.api.outputDir);

      // Generate TypeDoc documentation
      const typedocArgs = [
        'typedoc',
        '--entryPoints', path.join(config.paths.src, 'index.ts'),
        '--out', config.documentation.api.outputDir,
        '--theme', config.documentation.api.theme,
        '--excludePrivate',
        '--excludeProtected',
        '--excludeInternal',
        '--readme', path.join(config.paths.root, 'README.md'),
        '--name', `${config.project.name} API Documentation`,
      ];

      if (this.options.verbose) {
        typedocArgs.push('--logLevel', 'Verbose');
      }

      await processUtils.exec(
        `npx ${typedocArgs.join(' ')}`,
        { cwd: config.paths.root }
      );

      const duration = timer.end();
      this.results.api = { success: true, duration };
      logger.success(`API documentation generated in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.api = { success: false, duration, error: error.message };
      logger.error('API documentation generation failed:', error.message);
      throw error;
    }
  }

  /**
   * Extract component props documentation
   */
  async extractProps() {
    if (!this.options.props && !this.isRunningAll()) {
      return;
    }

    if (!config.documentation.props.enabled) {
      logger.warn('Props extraction is disabled');
      return;
    }

    logger.step('Extracting component props...');
    const timer = performanceUtils.createTimer('props-extraction');

    try {
      const componentFiles = await fileUtils.findFiles(
        path.join(config.paths.components, '**/*.tsx'),
        { ignore: ['**/*.test.tsx', '**/*.stories.tsx'] }
      );

      const propsData = {};

      for (const componentFile of componentFiles) {
        const componentName = path.basename(componentFile, '.tsx');
        
        // Skip if not a component file (starts with uppercase)
        if (!/^[A-Z]/.test(componentName)) {
          continue;
        }

        try {
          const props = await this.extractComponentProps(componentFile);
          if (props && Object.keys(props).length > 0) {
            propsData[componentName] = props;
          }
        } catch (error) {
          logger.warn(`Failed to extract props for ${componentName}:`, error.message);
        }
      }

      // Save props data
      await fileUtils.writeJson(config.documentation.props.outputFile, {
        timestamp: new Date().toISOString(),
        components: propsData,
      });

      const duration = timer.end();
      this.results.props = { success: true, duration, count: Object.keys(propsData).length };
      logger.success(`Props extracted for ${Object.keys(propsData).length} components in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.props = { success: false, duration, error: error.message };
      logger.error('Props extraction failed:', error.message);
      throw error;
    }
  }

  /**
   * Extract props from a component file
   */
  async extractComponentProps(componentFile) {
    // This is a simplified implementation
    // In a real scenario, you'd use react-docgen or similar tools
    const content = await fileUtils.readFile(componentFile, 'utf8');
    
    // Look for interface definitions
    const interfaceRegex = /interface\s+(\w+Props)\s*{([^}]+)}/g;
    const props = {};
    
    let match;
    while ((match = interfaceRegex.exec(content)) !== null) {
      const interfaceName = match[1];
      const interfaceBody = match[2];
      
      // Parse properties (simplified)
      const propRegex = /(\w+)(\?)?:\s*([^;]+);/g;
      let propMatch;
      
      while ((propMatch = propRegex.exec(interfaceBody)) !== null) {
        const propName = propMatch[1];
        const isOptional = !!propMatch[2];
        const propType = propMatch[3].trim();
        
        props[propName] = {
          type: propType,
          required: !isOptional,
          description: '', // Would be extracted from JSDoc comments
        };
      }
    }
    
    return props;
  }

  /**
   * Build design system documentation
   */
  async buildDesignSystem() {
    if (!this.options.designSystem && !this.isRunningAll()) {
      return;
    }

    if (!config.documentation.designSystem.enabled) {
      logger.warn('Design system documentation is disabled');
      return;
    }

    logger.step('Building design system documentation...');
    const timer = performanceUtils.createTimer('design-system');

    try {
      // Ensure output directory exists
      await fileUtils.ensureDir(config.documentation.designSystem.outputDir);

      // Extract design tokens
      const tokens = await this.extractDesignTokens();
      
      // Generate design system documentation
      await this.generateDesignSystemDocs(tokens);

      const duration = timer.end();
      this.results.designSystem = { success: true, duration };
      logger.success(`Design system documentation built in ${duration}ms`);

    } catch (error) {
      const duration = timer.end();
      this.results.designSystem = { success: false, duration, error: error.message };
      logger.error('Design system documentation build failed:', error.message);
      throw error;
    }
  }

  /**
   * Extract design tokens from theme configuration
   */
  async extractDesignTokens() {
    try {
      const tokensFile = config.documentation.designSystem.tokensFile;
      
      if (!(await fileUtils.pathExists(tokensFile))) {
        logger.warn('Design tokens file not found, creating placeholder');
        return {
          colors: {},
          spacing: {},
          typography: {},
          animations: {},
        };
      }

      // This would parse the actual tokens file
      // For now, return a placeholder structure
      return {
        colors: {
          primary: '#007AFF',
          secondary: '#5856D6',
          success: '#34C759',
          warning: '#FF9500',
          error: '#FF3B30',
        },
        spacing: {
          xs: 4,
          sm: 8,
          md: 16,
          lg: 24,
          xl: 32,
        },
        typography: {
          sizes: {
            xs: 12,
            sm: 14,
            md: 16,
            lg: 18,
            xl: 20,
          },
          weights: {
            normal: 400,
            medium: 500,
            semibold: 600,
            bold: 700,
          },
        },
        animations: {
          fast: 150,
          normal: 300,
          slow: 500,
        },
      };
    } catch (error) {
      logger.warn('Failed to extract design tokens:', error.message);
      return {};
    }
  }

  /**
   * Generate design system documentation files
   */
  async generateDesignSystemDocs(tokens) {
    const docsDir = config.documentation.designSystem.outputDir;

    // Generate index page
    const indexContent = this.generateDesignSystemIndex(tokens);
    await fileUtils.writeFile(path.join(docsDir, 'index.html'), indexContent);

    // Generate tokens JSON
    await fileUtils.writeJson(path.join(docsDir, 'tokens.json'), tokens);

    logger.debug('Design system documentation files generated');
  }

  /**
   * Generate design system index HTML
   */
  generateDesignSystemIndex(tokens) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${config.project.name} Design System</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin-bottom: 40px; }
        .token-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 16px; }
        .token-card { border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; }
        .color-swatch { width: 40px; height: 40px; border-radius: 4px; margin-bottom: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>${config.project.name} Design System</h1>
        <p>Generated on ${new Date().toLocaleDateString()}</p>
        
        <div class="section">
            <h2>Colors</h2>
            <div class="token-grid">
                ${Object.entries(tokens.colors || {}).map(([name, value]) => `
                    <div class="token-card">
                        <div class="color-swatch" style="background-color: ${value}"></div>
                        <strong>${name}</strong><br>
                        <code>${value}</code>
                    </div>
                `).join('')}
            </div>
        </div>
        
        <div class="section">
            <h2>Spacing</h2>
            <div class="token-grid">
                ${Object.entries(tokens.spacing || {}).map(([name, value]) => `
                    <div class="token-card">
                        <strong>${name}</strong><br>
                        <code>${value}px</code>
                    </div>
                `).join('')}
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Serve documentation
   */
  async serveDocs() {
    if (!this.options.serve) {
      return;
    }

    logger.step(`Starting documentation server on port ${this.options.port}...`);

    try {
      const express = require('express');
      const app = express();

      // Serve Storybook if available
      if (await fileUtils.pathExists(config.documentation.storybook.outputDir)) {
        app.use('/storybook', express.static(config.documentation.storybook.outputDir));
      }

      // Serve API docs if available
      if (await fileUtils.pathExists(config.documentation.api.outputDir)) {
        app.use('/api', express.static(config.documentation.api.outputDir));
      }

      // Serve design system docs if available
      if (await fileUtils.pathExists(config.documentation.designSystem.outputDir)) {
        app.use('/design-system', express.static(config.documentation.designSystem.outputDir));
      }

      // Root redirect
      app.get('/', (req, res) => {
        res.send(`
          <h1>${config.project.name} Documentation</h1>
          <ul>
            <li><a href="/storybook">Storybook</a></li>
            <li><a href="/api">API Documentation</a></li>
            <li><a href="/design-system">Design System</a></li>
          </ul>
        `);
      });

      app.listen(this.options.port, () => {
        logger.success(`Documentation server running at http://localhost:${this.options.port}`);
      });

    } catch (error) {
      logger.error('Failed to start documentation server:', error.message);
      throw error;
    }
  }

  /**
   * Check if running all documentation builds
   */
  isRunningAll() {
    return !this.options.storybook && 
           !this.options.api && 
           !this.options.props && 
           !this.options.designSystem;
  }

  /**
   * Generate documentation summary
   */
  generateSummary() {
    logger.info('\n📚 Documentation Summary:');
    
    const results = Object.entries(this.results).filter(([_, result]) => result !== null);
    
    if (results.length === 0) {
      logger.warn('No documentation was built');
      return;
    }

    let totalDuration = 0;
    let successCount = 0;
    let failureCount = 0;

    results.forEach(([docType, result]) => {
      const status = result.success ? '✅' : '❌';
      const duration = result.duration || 0;
      totalDuration += duration;
      
      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }

      let extra = '';
      if (docType === 'props' && result.count) {
        extra = ` (${result.count} components)`;
      }

      logger.info(`   ${status} ${docType}: ${duration}ms${extra}`);
      
      if (!result.success && result.error) {
        logger.error(`      Error: ${result.error}`);
      }
    });

    logger.info(`\n⏱️  Total time: ${totalDuration}ms`);
    logger.info(`✅ Built: ${successCount}`);
    logger.info(`❌ Failed: ${failureCount}`);

    if (failureCount > 0) {
      logger.error(`\n${failureCount} documentation build(s) failed`);
      process.exit(1);
    } else {
      logger.success(`\nAll ${successCount} documentation build(s) completed!`);
    }
  }

  /**
   * Main documentation build process
   */
  async build() {
    const overallTimer = performanceUtils.createTimer('docs-build');
    
    try {
      logger.info('📚 Building HVPPYPlug UI Components documentation...');

      // Build documentation based on options
      if (this.isRunningAll()) {
        await this.buildStorybook();
        await this.buildApiDocs();
        await this.extractProps();
        await this.buildDesignSystem();
      } else {
        if (this.options.storybook) {
          await this.buildStorybook();
        }
        if (this.options.api) {
          await this.buildApiDocs();
        }
        if (this.options.props) {
          await this.extractProps();
        }
        if (this.options.designSystem) {
          await this.buildDesignSystem();
        }
      }

      const duration = overallTimer.end();
      const memory = performanceUtils.getMemoryUsage();

      logger.debug(`Total build time: ${duration}ms`);
      logger.debug(`Memory usage: ${memory.heapUsed}MB`);

      this.generateSummary();

      // Start server if requested
      if (this.options.serve) {
        await this.serveDocs();
      }

    } catch (error) {
      logger.error('Documentation build failed:', error.message);
      this.generateSummary();
      throw error;
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    const builder = new DocumentationBuilder(argv);
    await builder.build();
  } catch (error) {
    logger.error('Documentation build process failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { DocumentationBuilder };

#!/usr/bin/env node

/**
 * Clean Script for HVPPYPlug UI Components
 * 
 * Removes build artifacts, temporary files, and cached data to ensure
 * a clean development environment.
 * 
 * Usage:
 *   node scripts/clean.js [options]
 *   pnpm run clean [options]
 * 
 * Options:
 *   --all, -a          Clean everything including node_modules
 *   --cache            Clean only cache files
 *   --build            Clean only build artifacts
 *   --docs             Clean documentation builds
 *   --verbose, -v      Verbose output
 *   --help, -h         Show help information
 * 
 * Examples:
 *   node scripts/clean.js
 *   node scripts/clean.js --all
 *   node scripts/clean.js --cache --docs
 */

const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const path = require('path');
const {
  logger,
  fileUtils,
  performanceUtils,
  config,
} = require('./utils');

// Command line argument parsing
const argv = yargs(hideBin(process.argv))
  .usage('Usage: $0 [options]')
  .option('all', {
    alias: 'a',
    describe: 'Clean everything including node_modules',
    type: 'boolean',
    default: false,
  })
  .option('cache', {
    describe: 'Clean only cache files',
    type: 'boolean',
    default: false,
  })
  .option('build', {
    describe: 'Clean only build artifacts',
    type: 'boolean',
    default: false,
  })
  .option('docs', {
    describe: 'Clean documentation builds',
    type: 'boolean',
    default: false,
  })
  .option('verbose', {
    alias: 'v',
    describe: 'Verbose output',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .example('$0', 'Clean build artifacts and cache')
  .example('$0 --all', 'Clean everything including dependencies')
  .argv;

/**
 * Cleaner utility class
 */
class Cleaner {
  constructor(options = {}) {
    this.options = options;
    this.cleanedItems = [];
    this.errors = [];
  }

  /**
   * Clean build artifacts
   */
  async cleanBuildArtifacts() {
    if (this.options.cache || this.options.docs) {
      return;
    }

    logger.step('Cleaning build artifacts...');

    const buildPaths = [
      config.paths.dist,
      path.join(config.paths.root, 'lib'),
      path.join(config.paths.root, 'build'),
      ...Object.values(config.build.outputs),
    ];

    for (const buildPath of buildPaths) {
      await this.cleanPath(buildPath, 'build artifact');
    }

    // Clean TypeScript build info files
    const tsBuildInfoPatterns = [
      '*.tsbuildinfo',
      'tsconfig.tsbuildinfo',
      'src/**/*.tsbuildinfo',
    ];

    for (const pattern of tsBuildInfoPatterns) {
      await this.cleanPattern(pattern, 'TypeScript build info');
    }
  }

  /**
   * Clean cache files
   */
  async cleanCache() {
    if (this.options.build || this.options.docs) {
      return;
    }

    logger.step('Cleaning cache files...');

    const cachePaths = [
      path.join(config.paths.root, '.eslintcache'),
      path.join(config.paths.root, '.tscache'),
      path.join(config.paths.root, 'node_modules/.cache'),
      path.join(config.paths.root, '.rollup.cache'),
      path.join(config.paths.root, '.jest-cache'),
    ];

    for (const cachePath of cachePaths) {
      await this.cleanPath(cachePath, 'cache');
    }

    // Clean test coverage
    await this.cleanPath(
      path.join(config.paths.root, 'coverage'),
      'test coverage'
    );

    // Clean temporary files
    const tempPatterns = [
      '*.tmp',
      '*.temp',
      '.DS_Store',
      'Thumbs.db',
      '*.log',
    ];

    for (const pattern of tempPatterns) {
      await this.cleanPattern(pattern, 'temporary file');
    }
  }

  /**
   * Clean documentation builds
   */
  async cleanDocs() {
    if (this.options.cache || this.options.build) {
      return;
    }

    logger.step('Cleaning documentation builds...');

    const docsPaths = [
      config.paths.docs,
      config.documentation.storybook.outputDir,
      config.documentation.api.outputDir,
      config.documentation.designSystem.outputDir,
      path.join(config.paths.root, 'storybook-static'),
    ];

    for (const docsPath of docsPaths) {
      await this.cleanPath(docsPath, 'documentation');
    }
  }

  /**
   * Clean node_modules and lock files
   */
  async cleanDependencies() {
    if (!this.options.all) {
      return;
    }

    logger.step('Cleaning dependencies...');

    const depPaths = [
      path.join(config.paths.root, 'node_modules'),
      path.join(config.paths.root, 'pnpm-lock.yaml'),
      path.join(config.paths.root, 'package-lock.json'),
      path.join(config.paths.root, 'yarn.lock'),
    ];

    for (const depPath of depPaths) {
      await this.cleanPath(depPath, 'dependency');
    }
  }

  /**
   * Clean maintenance patterns from config
   */
  async cleanMaintenancePatterns() {
    if (this.options.cache || this.options.build || this.options.docs) {
      return;
    }

    logger.step('Cleaning maintenance patterns...');

    for (const pattern of config.maintenance.cleanup.patterns) {
      await this.cleanPattern(pattern, 'maintenance pattern');
    }
  }

  /**
   * Clean a specific path
   */
  async cleanPath(targetPath, type = 'item') {
    try {
      if (await fileUtils.pathExists(targetPath)) {
        const stats = await fileUtils.stat(targetPath);
        const size = this.formatSize(stats.isDirectory() ? await this.getDirectorySize(targetPath) : stats.size);
        
        await fileUtils.remove(targetPath);
        
        this.cleanedItems.push({
          path: path.relative(config.paths.root, targetPath),
          type,
          size,
        });
        
        if (this.options.verbose) {
          logger.debug(`Cleaned ${type}: ${path.relative(config.paths.root, targetPath)} (${size})`);
        }
      }
    } catch (error) {
      this.errors.push({
        path: targetPath,
        type,
        error: error.message,
      });
      
      if (this.options.verbose) {
        logger.warn(`Failed to clean ${type}: ${path.relative(config.paths.root, targetPath)} - ${error.message}`);
      }
    }
  }

  /**
   * Clean files matching a pattern
   */
  async cleanPattern(pattern, type = 'pattern') {
    try {
      const files = await fileUtils.findFiles(pattern, {
        cwd: config.paths.root,
        absolute: true,
      });

      for (const file of files) {
        await this.cleanPath(file, type);
      }
    } catch (error) {
      this.errors.push({
        path: pattern,
        type,
        error: error.message,
      });
      
      if (this.options.verbose) {
        logger.warn(`Failed to clean pattern ${pattern}: ${error.message}`);
      }
    }
  }

  /**
   * Get directory size recursively
   */
  async getDirectorySize(dirPath) {
    try {
      const files = await fileUtils.findFiles('**/*', {
        cwd: dirPath,
        absolute: true,
        nodir: true,
      });

      let totalSize = 0;
      for (const file of files) {
        try {
          const stats = await fileUtils.stat(file);
          totalSize += stats.size;
        } catch {
          // Ignore files that can't be accessed
        }
      }

      return totalSize;
    } catch {
      return 0;
    }
  }

  /**
   * Format file size for display
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    const base = 1024;
    const unitIndex = Math.floor(Math.log(bytes) / Math.log(base));
    const size = bytes / Math.pow(base, unitIndex);
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * Check if running specific clean operations
   */
  isRunningSpecific() {
    return this.options.cache || this.options.build || this.options.docs;
  }

  /**
   * Generate cleaning summary
   */
  generateSummary() {
    logger.info('\n🧹 Cleaning Summary:');
    
    if (this.cleanedItems.length === 0) {
      logger.info('   No items were cleaned');
      return;
    }

    // Group by type
    const groupedItems = this.cleanedItems.reduce((groups, item) => {
      if (!groups[item.type]) {
        groups[item.type] = [];
      }
      groups[item.type].push(item);
      return groups;
    }, {});

    let totalSize = 0;
    Object.entries(groupedItems).forEach(([type, items]) => {
      logger.info(`   ${type}: ${items.length} items`);
      
      if (this.options.verbose) {
        items.forEach(item => {
          logger.info(`     - ${item.path} (${item.size})`);
        });
      }
    });

    logger.info(`\n✅ Cleaned ${this.cleanedItems.length} items`);

    if (this.errors.length > 0) {
      logger.warn(`⚠️  ${this.errors.length} errors occurred`);
      
      if (this.options.verbose) {
        logger.info('\nErrors:');
        this.errors.forEach(error => {
          logger.error(`   ${error.path}: ${error.error}`);
        });
      }
    }
  }

  /**
   * Main cleaning process
   */
  async clean() {
    const timer = performanceUtils.createTimer('clean');
    
    try {
      logger.info('🧹 Cleaning HVPPYPlug UI Components...');

      if (this.isRunningSpecific()) {
        // Run specific cleaning operations
        if (this.options.cache) {
          await this.cleanCache();
        }
        if (this.options.build) {
          await this.cleanBuildArtifacts();
        }
        if (this.options.docs) {
          await this.cleanDocs();
        }
      } else {
        // Run all cleaning operations
        await this.cleanBuildArtifacts();
        await this.cleanCache();
        await this.cleanDocs();
        await this.cleanMaintenancePatterns();
        await this.cleanDependencies();
      }

      const duration = timer.end();
      logger.success(`Cleaning completed in ${duration}ms`);

      this.generateSummary();

    } catch (error) {
      logger.error('Cleaning failed:', error.message);
      throw error;
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    const cleaner = new Cleaner(argv);
    await cleaner.clean();
  } catch (error) {
    logger.error('Clean process failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { Cleaner };

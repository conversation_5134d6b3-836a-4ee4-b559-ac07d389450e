/**
 * Configuration for HVPPYPlug UI Components utility scripts
 * 
 * This file contains all configuration options for the various utility scripts
 * used in the development, building, testing, and maintenance of the component library.
 */

const path = require('path');
const fs = require('fs');

// Base paths
const ROOT_DIR = path.resolve(__dirname, '..');
const SRC_DIR = path.join(ROOT_DIR, 'src');
const DIST_DIR = path.join(ROOT_DIR, 'dist');
const SCRIPTS_DIR = path.join(ROOT_DIR, 'scripts');
const TEMPLATES_DIR = path.join(SCRIPTS_DIR, 'templates');
const DOCS_DIR = path.join(ROOT_DIR, 'docs');
const STORYBOOK_DIR = path.join(ROOT_DIR, '.storybook');

// Package information
const packageJson = JSON.parse(fs.readFileSync(path.join(ROOT_DIR, 'package.json'), 'utf8'));

const config = {
  // Project metadata
  project: {
    name: packageJson.name,
    version: packageJson.version,
    description: packageJson.description,
    author: packageJson.author,
  },

  // Directory paths
  paths: {
    root: ROOT_DIR,
    src: SRC_DIR,
    dist: DIST_DIR,
    scripts: SCRIPTS_DIR,
    templates: TEMPLATES_DIR,
    docs: DOCS_DIR,
    storybook: STORYBOOK_DIR,
    components: path.join(SRC_DIR, 'components'),
    hooks: path.join(SRC_DIR, 'hooks'),
    utils: path.join(SRC_DIR, 'utils'),
    types: path.join(SRC_DIR, 'types'),
    animations: path.join(SRC_DIR, 'animations'),
    icons: path.join(SRC_DIR, 'icons'),
    config: path.join(SRC_DIR, 'config'),
  },

  // Build configuration
  build: {
    // Output directories
    outputs: {
      esm: path.join(DIST_DIR, 'esm'),
      cjs: path.join(DIST_DIR, 'cjs'),
      types: path.join(DIST_DIR, 'types'),
      native: path.join(DIST_DIR, 'native'),
    },
    
    // File patterns
    patterns: {
      source: 'src/**/*.{ts,tsx}',
      tests: 'src/**/*.{test,spec}.{ts,tsx}',
      stories: 'src/**/*.stories.{ts,tsx}',
      exclude: [
        'src/**/*.{test,spec}.{ts,tsx}',
        'src/**/*.stories.{ts,tsx}',
        'src/**/test-utils.{ts,tsx}',
        'src/**/__tests__/**',
        'src/**/__mocks__/**',
      ],
    },

    // TypeScript configuration
    typescript: {
      configFile: path.join(ROOT_DIR, 'tsconfig.json'),
      buildConfigFile: path.join(ROOT_DIR, 'tsconfig.build.json'),
      declarationDir: path.join(DIST_DIR, 'types'),
    },

    // Bundle analysis
    bundleAnalysis: {
      enabled: true,
      outputFile: path.join(DIST_DIR, 'bundle-analysis.json'),
      sizeLimit: {
        warning: '500kb',
        error: '1mb',
      },
    },
  },

  // Component generation
  generation: {
    // Available templates
    templates: {
      basic: 'basic-component',
      form: 'form-component',
      compound: 'compound-component',
      animation: 'animation-component',
      hook: 'custom-hook',
    },

    // Default template options
    defaults: {
      template: 'basic',
      withTests: true,
      withStories: true,
      withDocs: true,
      typescript: true,
    },

    // File naming conventions
    naming: {
      component: 'PascalCase',
      file: 'PascalCase',
      directory: 'kebab-case',
      test: '{name}.test.tsx',
      story: '{name}.stories.tsx',
      types: '{name}.types.ts',
    },
  },

  // Testing configuration
  testing: {
    // Jest configuration
    jest: {
      configFile: path.join(ROOT_DIR, 'jest.config.js'),
      setupFile: path.join(ROOT_DIR, 'jest.setup.js'),
      coverageThreshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },

    // Test patterns
    patterns: {
      unit: 'src/**/*.{test,spec}.{ts,tsx}',
      integration: 'src/**/*.integration.{test,spec}.{ts,tsx}',
      e2e: 'e2e/**/*.{test,spec}.{ts,tsx}',
    },

    // Testing utilities
    utilities: {
      customRender: path.join(SRC_DIR, 'test-utils', 'render.tsx'),
      mockProviders: path.join(SRC_DIR, 'test-utils', 'providers.tsx'),
      testData: path.join(SRC_DIR, 'test-utils', 'data.ts'),
    },

    // Visual regression testing
    visual: {
      enabled: true,
      threshold: 0.2,
      outputDir: path.join(ROOT_DIR, '__visual_tests__'),
    },

    // Accessibility testing
    accessibility: {
      enabled: true,
      rules: 'wcag2a',
      outputFile: path.join(ROOT_DIR, 'accessibility-report.json'),
    },
  },

  // Documentation configuration
  documentation: {
    // Storybook
    storybook: {
      configDir: STORYBOOK_DIR,
      outputDir: path.join(DOCS_DIR, 'storybook'),
      port: 6006,
      host: 'localhost',
    },

    // API documentation
    api: {
      enabled: true,
      outputDir: path.join(DOCS_DIR, 'api'),
      format: 'html',
      theme: 'default',
    },

    // Prop extraction
    props: {
      enabled: true,
      outputFile: path.join(DOCS_DIR, 'props.json'),
      includePrivate: false,
    },

    // Design system documentation
    designSystem: {
      enabled: true,
      tokensFile: path.join(SRC_DIR, 'config', 'tokens.ts'),
      outputDir: path.join(DOCS_DIR, 'design-system'),
    },
  },

  // Quality assurance
  quality: {
    // ESLint configuration
    eslint: {
      configFile: path.join(ROOT_DIR, '.eslintrc.js'),
      extensions: ['.ts', '.tsx', '.js', '.jsx'],
      ignoreFile: path.join(ROOT_DIR, '.eslintignore'),
    },

    // Prettier configuration
    prettier: {
      configFile: path.join(ROOT_DIR, '.prettierrc'),
      ignoreFile: path.join(ROOT_DIR, '.prettierignore'),
    },

    // Type checking
    typeCheck: {
      strict: true,
      noEmit: true,
      incremental: true,
    },

    // Security audit
    security: {
      enabled: true,
      level: 'moderate',
      outputFile: path.join(ROOT_DIR, 'security-audit.json'),
    },
  },

  // Development server
  development: {
    // Watch mode configuration
    watch: {
      patterns: ['src/**/*.{ts,tsx}'],
      ignored: ['**/*.{test,spec}.{ts,tsx}', '**/node_modules/**'],
      debounce: 300,
    },

    // Hot reloading
    hotReload: {
      enabled: true,
      port: 3000,
      host: 'localhost',
    },

    // Development utilities
    utilities: {
      openBrowser: true,
      clearConsole: true,
      showProgress: true,
    },
  },

  // Performance monitoring
  performance: {
    // Bundle size monitoring
    bundleSize: {
      enabled: true,
      warnThreshold: '500kb',
      errorThreshold: '1mb',
    },

    // Build time monitoring
    buildTime: {
      enabled: true,
      warnThreshold: 30000, // 30 seconds
      errorThreshold: 60000, // 1 minute
    },

    // Memory usage monitoring
    memory: {
      enabled: true,
      warnThreshold: '512mb',
      errorThreshold: '1gb',
    },
  },

  // Maintenance
  maintenance: {
    // Dependency updates
    dependencies: {
      autoUpdate: false,
      schedule: 'weekly',
      excludePatterns: ['@types/*'],
    },

    // Health checks
    healthCheck: {
      enabled: true,
      checks: [
        'dependencies',
        'typescript',
        'tests',
        'build',
        'exports',
      ],
    },

    // Cleanup
    cleanup: {
      patterns: [
        'dist/**',
        'coverage/**',
        '*.tsbuildinfo',
        '.eslintcache',
      ],
    },
  },

  // Logging and output
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    colors: true,
    timestamps: true,
    outputFile: path.join(ROOT_DIR, 'scripts.log'),
  },

  // Environment-specific overrides
  environments: {
    development: {
      logging: { level: 'debug' },
      build: { minify: false, sourceMaps: true },
    },
    production: {
      logging: { level: 'warn' },
      build: { minify: true, sourceMaps: false },
    },
    ci: {
      logging: { level: 'info', colors: false },
      testing: { coverage: true, verbose: true },
    },
  },
};

// Apply environment-specific overrides
const env = process.env.NODE_ENV || 'development';
if (config.environments[env]) {
  Object.assign(config, config.environments[env]);
}

module.exports = config;

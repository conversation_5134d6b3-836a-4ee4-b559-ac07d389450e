# HVPPYPlug UI Components - Utility Scripts

This directory contains utility scripts for developing, building, testing, and maintaining the HVPPYPlug UI components library.

## Quick Start

```bash
# Install dependencies
pnpm install

# Generate a new component
pnpm run generate:component MyComponent

# Start development mode
pnpm run dev

# Run tests
pnpm run test

# Build the library
pnpm run build

# Generate documentation
pnpm run docs:build
```

## Script Categories

### 🏗️ Build & Development
- `build.js` - Production build with optimization
- `dev.js` - Development server with hot reloading
- `watch.js` - File watching for development
- `clean.js` - Clean build artifacts

### 🧩 Component Generation
- `generate-component.js` - Create new components with templates
- `generate-story.js` - Generate Storybook stories
- `validate-component.js` - Validate component structure

### 🧪 Testing
- `test.js` - Run all tests with coverage
- `test-watch.js` - Run tests in watch mode
- `test-visual.js` - Visual regression testing
- `test-a11y.js` - Accessibility testing

### 📚 Documentation
- `docs-build.js` - Build documentation site
- `docs-dev.js` - Development documentation server
- `extract-props.js` - Extract component props for docs

### 🔍 Quality Assurance
- `lint.js` - Code linting and formatting
- `type-check.js` - TypeScript validation
- `analyze-bundle.js` - Bundle size analysis
- `security-audit.js` - Security vulnerability scanning

### 🛠️ Utilities
- `health-check.js` - Library health validation
- `update-dependencies.js` - Automated dependency updates
- `profile-performance.js` - Performance profiling

## Usage Examples

### Generate a New Component
```bash
# Basic component
node scripts/generate-component.js Button

# Component with specific template
node scripts/generate-component.js Modal --template=compound

# Component with all files
node scripts/generate-component.js Input --with-tests --with-stories --with-docs
```

### Development Workflow
```bash
# Start development with hot reloading
node scripts/dev.js

# Run tests in watch mode
node scripts/test-watch.js

# Type check in watch mode
node scripts/type-check.js --watch
```

### Build & Release
```bash
# Clean build
node scripts/clean.js && node scripts/build.js

# Analyze bundle size
node scripts/analyze-bundle.js

# Run full quality checks
node scripts/lint.js && node scripts/test.js && node scripts/type-check.js
```

## Configuration

Scripts can be configured through:
- `scripts/config.js` - Main configuration file
- Environment variables
- Command line arguments
- Package.json settings

## Integration with pnpm Workspace

All scripts are designed to work seamlessly with the pnpm workspace structure:
- Respect workspace dependencies
- Use workspace-aware commands
- Support cross-package operations
- Maintain workspace integrity

## Contributing

When adding new scripts:
1. Follow the existing naming conventions
2. Add comprehensive error handling
3. Include usage documentation
4. Add tests for complex logic
5. Update this README

## Troubleshooting

### Common Issues

**Build Failures:**
- Check TypeScript configuration
- Verify all dependencies are installed
- Clear node_modules and reinstall

**Test Failures:**
- Ensure React Native Testing Library is properly configured
- Check for missing test setup files
- Verify mock configurations

**Documentation Issues:**
- Check Storybook configuration
- Verify component exports
- Ensure prop types are properly defined

### Getting Help

1. Check the script's `--help` option
2. Review the implementation plan documentation
3. Check the troubleshooting section in each script
4. Open an issue with detailed error information

## Script Dependencies

Core dependencies used across scripts:
- `chalk` - Terminal colors and styling
- `fs-extra` - Enhanced file system operations
- `inquirer` - Interactive command line prompts
- `yargs` - Command line argument parsing
- `glob` - File pattern matching
- `chokidar` - File watching
- `execa` - Process execution

## Performance Considerations

Scripts are optimized for:
- Fast execution times
- Minimal memory usage
- Efficient file operations
- Parallel processing where appropriate
- Incremental builds and testing

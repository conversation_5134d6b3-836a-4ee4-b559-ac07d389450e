#!/usr/bin/env node

/**
 * Health Check Script for HVPPYPlug UI Components
 * 
 * Performs comprehensive health checks on the component library including:
 * - Build system validation
 * - Dependency analysis
 * - TypeScript configuration
 * - Test coverage analysis
 * - Export validation
 * - Documentation completeness
 * 
 * Usage:
 *   node scripts/health-check.js [options]
 *   pnpm run health-check [options]
 * 
 * Options:
 *   --fix              Attempt to fix issues automatically
 *   --report           Generate detailed health report
 *   --json             Output results in JSON format
 *   --verbose, -v      Verbose output
 *   --help, -h         Show help information
 * 
 * Examples:
 *   node scripts/health-check.js
 *   node scripts/health-check.js --fix --report
 *   node scripts/health-check.js --json > health-report.json
 */

const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const path = require('path');
const {
  logger,
  fileUtils,
  processUtils,
  performanceUtils,
  validationUtils,
  config,
} = require('./utils');

// Command line argument parsing
const argv = yargs(hideBin(process.argv))
  .usage('Usage: $0 [options]')
  .option('fix', {
    describe: 'Attempt to fix issues automatically',
    type: 'boolean',
    default: false,
  })
  .option('report', {
    describe: 'Generate detailed health report',
    type: 'boolean',
    default: false,
  })
  .option('json', {
    describe: 'Output results in JSON format',
    type: 'boolean',
    default: false,
  })
  .option('verbose', {
    alias: 'v',
    describe: 'Verbose output',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .example('$0', 'Run basic health checks')
  .example('$0 --fix --report', 'Fix issues and generate report')
  .argv;

/**
 * Health checker class
 */
class HealthChecker {
  constructor(options = {}) {
    this.options = options;
    this.results = {
      overall: { score: 0, status: 'unknown' },
      checks: {},
      issues: [],
      recommendations: [],
    };
  }

  /**
   * Check build system health
   */
  async checkBuildSystem() {
    const checkName = 'Build System';
    logger.step(`Checking ${checkName}...`);

    const issues = [];
    const recommendations = [];
    let score = 100;

    try {
      // Check TypeScript configuration
      const tsConfigPath = config.build.typescript.configFile;
      if (!(await fileUtils.pathExists(tsConfigPath))) {
        issues.push('TypeScript configuration file missing');
        score -= 30;
      } else {
        const tsConfig = await fileUtils.readJson(tsConfigPath);
        if (!tsConfig.compilerOptions) {
          issues.push('TypeScript configuration incomplete');
          score -= 20;
        }
      }

      // Check build scripts
      const packageJson = await fileUtils.readJson(path.join(config.paths.root, 'package.json'));
      const requiredScripts = ['build', 'dev', 'clean', 'type-check'];
      const missingScripts = requiredScripts.filter(script => !packageJson.scripts[script]);
      
      if (missingScripts.length > 0) {
        issues.push(`Missing build scripts: ${missingScripts.join(', ')}`);
        score -= missingScripts.length * 10;
      }

      // Check output directories
      const outputDirs = Object.values(config.build.outputs);
      for (const dir of outputDirs) {
        if (await fileUtils.pathExists(dir)) {
          const files = await fileUtils.findFiles(path.join(dir, '**/*'));
          if (files.length === 0) {
            recommendations.push(`Output directory ${path.relative(config.paths.root, dir)} is empty`);
          }
        }
      }

      this.results.checks[checkName] = {
        score: Math.max(0, score),
        status: score >= 80 ? 'good' : score >= 60 ? 'warning' : 'error',
        issues,
        recommendations,
      };

    } catch (error) {
      this.results.checks[checkName] = {
        score: 0,
        status: 'error',
        issues: [`Build system check failed: ${error.message}`],
        recommendations: ['Review build configuration and dependencies'],
      };
    }
  }

  /**
   * Check dependencies health
   */
  async checkDependencies() {
    const checkName = 'Dependencies';
    logger.step(`Checking ${checkName}...`);

    const issues = [];
    const recommendations = [];
    let score = 100;

    try {
      const packageJson = await fileUtils.readJson(path.join(config.paths.root, 'package.json'));

      // Check for security vulnerabilities
      try {
        await processUtils.exec('pnpm audit --audit-level high', { cwd: config.paths.root });
      } catch (error) {
        if (error.message.includes('vulnerabilities')) {
          issues.push('Security vulnerabilities found in dependencies');
          score -= 25;
          recommendations.push('Run `pnpm audit --fix` to resolve security issues');
        }
      }

      // Check for outdated dependencies
      try {
        const outdated = await processUtils.exec('pnpm outdated --format json', { cwd: config.paths.root });
        if (outdated.trim()) {
          const outdatedPackages = JSON.parse(outdated);
          const count = Object.keys(outdatedPackages).length;
          if (count > 0) {
            recommendations.push(`${count} dependencies are outdated`);
            score -= Math.min(20, count * 2);
          }
        }
      } catch (error) {
        // pnpm outdated exits with code 1 when outdated packages exist
        if (!error.message.includes('Command failed')) {
          logger.debug('Could not check for outdated dependencies');
        }
      }

      // Check peer dependencies
      if (packageJson.peerDependencies) {
        const peerDeps = Object.keys(packageJson.peerDependencies);
        recommendations.push(`Ensure peer dependencies are installed: ${peerDeps.join(', ')}`);
      }

      this.results.checks[checkName] = {
        score: Math.max(0, score),
        status: score >= 80 ? 'good' : score >= 60 ? 'warning' : 'error',
        issues,
        recommendations,
      };

    } catch (error) {
      this.results.checks[checkName] = {
        score: 0,
        status: 'error',
        issues: [`Dependencies check failed: ${error.message}`],
        recommendations: ['Review package.json and run `pnpm install`'],
      };
    }
  }

  /**
   * Check TypeScript configuration
   */
  async checkTypeScript() {
    const checkName = 'TypeScript';
    logger.step(`Checking ${checkName}...`);

    const issues = [];
    const recommendations = [];
    let score = 100;

    try {
      // Run type checking
      try {
        await processUtils.exec(
          `npx tsc --project ${config.build.typescript.configFile} --noEmit`,
          { cwd: config.paths.root }
        );
      } catch (error) {
        issues.push('TypeScript compilation errors found');
        score -= 40;
        recommendations.push('Fix TypeScript errors before proceeding');
      }

      // Check tsconfig.json structure
      const tsConfig = await fileUtils.readJson(config.build.typescript.configFile);
      const requiredOptions = ['strict', 'declaration', 'sourceMap'];
      const missingOptions = requiredOptions.filter(option => 
        tsConfig.compilerOptions && !tsConfig.compilerOptions[option]
      );

      if (missingOptions.length > 0) {
        recommendations.push(`Consider enabling TypeScript options: ${missingOptions.join(', ')}`);
        score -= missingOptions.length * 5;
      }

      this.results.checks[checkName] = {
        score: Math.max(0, score),
        status: score >= 80 ? 'good' : score >= 60 ? 'warning' : 'error',
        issues,
        recommendations,
      };

    } catch (error) {
      this.results.checks[checkName] = {
        score: 0,
        status: 'error',
        issues: [`TypeScript check failed: ${error.message}`],
        recommendations: ['Review TypeScript configuration'],
      };
    }
  }

  /**
   * Check test coverage
   */
  async checkTestCoverage() {
    const checkName = 'Test Coverage';
    logger.step(`Checking ${checkName}...`);

    const issues = [];
    const recommendations = [];
    let score = 100;

    try {
      // Find test files
      const testFiles = await fileUtils.findFiles(
        config.testing.patterns.unit,
        { cwd: config.paths.root }
      );

      // Find component files
      const componentFiles = await fileUtils.findFiles(
        path.join(config.paths.components, '**/*.tsx'),
        { 
          cwd: config.paths.root,
          ignore: ['**/*.test.tsx', '**/*.stories.tsx']
        }
      );

      const testCoverage = testFiles.length / Math.max(1, componentFiles.length);
      
      if (testCoverage < 0.8) {
        issues.push(`Low test coverage: ${Math.round(testCoverage * 100)}%`);
        score -= (0.8 - testCoverage) * 100;
        recommendations.push('Add more unit tests for components');
      }

      // Check for Jest configuration
      const jestConfigPath = config.testing.jest.configFile;
      if (!(await fileUtils.pathExists(jestConfigPath))) {
        issues.push('Jest configuration file missing');
        score -= 20;
        recommendations.push('Create Jest configuration file');
      }

      this.results.checks[checkName] = {
        score: Math.max(0, score),
        status: score >= 80 ? 'good' : score >= 60 ? 'warning' : 'error',
        issues,
        recommendations,
        metadata: {
          testFiles: testFiles.length,
          componentFiles: componentFiles.length,
          coverage: `${Math.round(testCoverage * 100)}%`,
        },
      };

    } catch (error) {
      this.results.checks[checkName] = {
        score: 0,
        status: 'error',
        issues: [`Test coverage check failed: ${error.message}`],
        recommendations: ['Review test configuration and setup'],
      };
    }
  }

  /**
   * Check exports validation
   */
  async checkExports() {
    const checkName = 'Exports';
    logger.step(`Checking ${checkName}...`);

    const issues = [];
    const recommendations = [];
    let score = 100;

    try {
      // Check main index file
      const indexPath = path.join(config.paths.src, 'index.ts');
      if (!(await fileUtils.pathExists(indexPath))) {
        issues.push('Main index.ts file missing');
        score -= 50;
      } else {
        const content = await fileUtils.readFile(indexPath, 'utf8');
        
        if (!content.includes('export')) {
          issues.push('No exports found in main index file');
          score -= 30;
        }

        // Check for common export issues
        const exportLines = content.split('\n').filter(line => line.trim().startsWith('export'));
        if (exportLines.length < 5) {
          recommendations.push('Consider adding more component exports');
          score -= 10;
        }
      }

      // Check package.json exports
      const packageJson = await fileUtils.readJson(path.join(config.paths.root, 'package.json'));
      if (!packageJson.main || !packageJson.types) {
        issues.push('Package.json missing main or types fields');
        score -= 20;
      }

      this.results.checks[checkName] = {
        score: Math.max(0, score),
        status: score >= 80 ? 'good' : score >= 60 ? 'warning' : 'error',
        issues,
        recommendations,
      };

    } catch (error) {
      this.results.checks[checkName] = {
        score: 0,
        status: 'error',
        issues: [`Exports check failed: ${error.message}`],
        recommendations: ['Review export configuration'],
      };
    }
  }

  /**
   * Check documentation completeness
   */
  async checkDocumentation() {
    const checkName = 'Documentation';
    logger.step(`Checking ${checkName}...`);

    const issues = [];
    const recommendations = [];
    let score = 100;

    try {
      // Check README
      const readmePath = path.join(config.paths.root, 'README.md');
      if (!(await fileUtils.pathExists(readmePath))) {
        issues.push('README.md file missing');
        score -= 30;
      }

      // Check component documentation
      const componentFiles = await fileUtils.findFiles(
        path.join(config.paths.components, '**/*.tsx'),
        { 
          cwd: config.paths.root,
          ignore: ['**/*.test.tsx', '**/*.stories.tsx']
        }
      );

      let documentedComponents = 0;
      for (const componentFile of componentFiles) {
        const componentName = path.basename(componentFile, '.tsx');
        if (!/^[A-Z]/.test(componentName)) continue;

        const componentDir = path.dirname(componentFile);
        const readmeFile = path.join(componentDir, 'README.md');
        
        if (await fileUtils.pathExists(readmeFile)) {
          documentedComponents++;
        }
      }

      const docCoverage = documentedComponents / Math.max(1, componentFiles.length);
      if (docCoverage < 0.5) {
        issues.push(`Low documentation coverage: ${Math.round(docCoverage * 100)}%`);
        score -= (0.5 - docCoverage) * 100;
        recommendations.push('Add README files for components');
      }

      // Check Storybook configuration
      if (!(await fileUtils.pathExists(config.documentation.storybook.configDir))) {
        recommendations.push('Consider setting up Storybook for interactive documentation');
        score -= 10;
      }

      this.results.checks[checkName] = {
        score: Math.max(0, score),
        status: score >= 80 ? 'good' : score >= 60 ? 'warning' : 'error',
        issues,
        recommendations,
        metadata: {
          documentedComponents,
          totalComponents: componentFiles.length,
          coverage: `${Math.round(docCoverage * 100)}%`,
        },
      };

    } catch (error) {
      this.results.checks[checkName] = {
        score: 0,
        status: 'error',
        issues: [`Documentation check failed: ${error.message}`],
        recommendations: ['Review documentation setup'],
      };
    }
  }

  /**
   * Calculate overall health score
   */
  calculateOverallScore() {
    const checks = Object.values(this.results.checks);
    if (checks.length === 0) {
      this.results.overall = { score: 0, status: 'error' };
      return;
    }

    const totalScore = checks.reduce((sum, check) => sum + check.score, 0);
    const averageScore = totalScore / checks.length;

    this.results.overall = {
      score: Math.round(averageScore),
      status: averageScore >= 80 ? 'good' : averageScore >= 60 ? 'warning' : 'error',
    };

    // Collect all issues and recommendations
    this.results.issues = checks.flatMap(check => check.issues || []);
    this.results.recommendations = checks.flatMap(check => check.recommendations || []);
  }

  /**
   * Generate health report
   */
  async generateReport() {
    if (!this.options.report) {
      return;
    }

    const reportPath = path.join(config.paths.root, 'health-report.md');
    const timestamp = new Date().toISOString();

    const report = `# Health Check Report

**Generated:** ${timestamp}  
**Overall Score:** ${this.results.overall.score}/100 (${this.results.overall.status})

## Summary

${Object.entries(this.results.checks).map(([name, check]) => 
  `- **${name}:** ${check.score}/100 (${check.status})`
).join('\n')}

## Issues Found

${this.results.issues.length > 0 
  ? this.results.issues.map(issue => `- ❌ ${issue}`).join('\n')
  : 'No issues found.'
}

## Recommendations

${this.results.recommendations.length > 0
  ? this.results.recommendations.map(rec => `- 💡 ${rec}`).join('\n')
  : 'No recommendations.'
}

## Detailed Results

${Object.entries(this.results.checks).map(([name, check]) => `
### ${name}

**Score:** ${check.score}/100  
**Status:** ${check.status}

${check.issues && check.issues.length > 0 ? `
**Issues:**
${check.issues.map(issue => `- ${issue}`).join('\n')}
` : ''}

${check.recommendations && check.recommendations.length > 0 ? `
**Recommendations:**
${check.recommendations.map(rec => `- ${rec}`).join('\n')}
` : ''}

${check.metadata ? `
**Metadata:**
${Object.entries(check.metadata).map(([key, value]) => `- ${key}: ${value}`).join('\n')}
` : ''}
`).join('\n')}
`;

    await fileUtils.writeFile(reportPath, report);
    logger.success(`Health report generated: ${reportPath}`);
  }

  /**
   * Output results
   */
  outputResults() {
    if (this.options.json) {
      console.log(JSON.stringify(this.results, null, 2));
      return;
    }

    logger.info('\n🏥 Health Check Results:');
    logger.info(`   Overall Score: ${this.results.overall.score}/100 (${this.results.overall.status})`);

    Object.entries(this.results.checks).forEach(([name, check]) => {
      const statusIcon = check.status === 'good' ? '✅' : check.status === 'warning' ? '⚠️' : '❌';
      logger.info(`   ${statusIcon} ${name}: ${check.score}/100`);
      
      if (this.options.verbose && check.issues && check.issues.length > 0) {
        check.issues.forEach(issue => {
          logger.warn(`      - ${issue}`);
        });
      }
    });

    if (this.results.issues.length > 0) {
      logger.warn(`\n⚠️  ${this.results.issues.length} issues found`);
    }

    if (this.results.recommendations.length > 0) {
      logger.info(`\n💡 ${this.results.recommendations.length} recommendations available`);
    }

    const overallStatus = this.results.overall.status;
    if (overallStatus === 'good') {
      logger.success('\n🎉 Library health is good!');
    } else if (overallStatus === 'warning') {
      logger.warn('\n⚠️  Library health needs attention');
    } else {
      logger.error('\n❌ Library health is poor - immediate action required');
    }
  }

  /**
   * Run all health checks
   */
  async runChecks() {
    const timer = performanceUtils.createTimer('health-check');

    try {
      logger.info('🏥 Running HVPPYPlug UI Components health checks...');

      await this.checkBuildSystem();
      await this.checkDependencies();
      await this.checkTypeScript();
      await this.checkTestCoverage();
      await this.checkExports();
      await this.checkDocumentation();

      this.calculateOverallScore();

      const duration = timer.end();
      logger.debug(`Health check completed in ${duration}ms`);

      await this.generateReport();
      this.outputResults();

      // Exit with error code if health is poor
      if (this.results.overall.status === 'error') {
        process.exit(1);
      }

    } catch (error) {
      logger.error('Health check failed:', error.message);
      throw error;
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    const checker = new HealthChecker(argv);
    await checker.runChecks();
  } catch (error) {
    logger.error('Health check process failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { HealthChecker };

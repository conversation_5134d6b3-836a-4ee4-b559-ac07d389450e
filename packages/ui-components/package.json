{"name": "@hvppyplug/ui-components", "version": "1.0.0", "description": "Shared UI components library for HVPPYPlug+ Expo React Native applications", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts", "./hooks": "./src/hooks/index.ts", "./utils": "./src/utils/index.ts", "./types": "./src/types/index.ts", "./config": "./src/config/index.ts", "./animations": "./src/animations/index.ts", "./icons": "./src/icons/index.ts", "./theme": "./src/config/theme.ts"}, "scripts": {"build": "tsc --build", "dev": "tsc --build --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@react-native-community/datetimepicker": "^8.2.0", "@tamagui/animations-react-native": "^1.112.21", "@tamagui/colors": "^1.112.21", "@tamagui/config": "^1.112.21", "@tamagui/core": "^1.112.21", "@tamagui/font-inter": "^1.112.21", "@tamagui/shorthands": "^1.112.21", "@tamagui/themes": "^1.112.21", "expo-blur": "~13.0.2", "expo-haptics": "~13.0.1", "lucide-react-native": "^0.447.0", "react-hook-form": "^7.53.0", "react-native-reanimated": "~3.10.1", "react-native-svg": "15.2.0", "tamagui": "^1.132.11", "zod": "^3.23.8"}, "devDependencies": {"@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.7.2", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "jest": "^29.7.0", "typescript": "~5.8.3"}, "peerDependencies": {"expo": "~53.0.0", "react": "^19.0.0", "react-native": "^0.79.0"}, "keywords": ["react-native", "expo", "ui-components", "tamagui", "design-system", "hvppyplug"], "author": "HVPPYPlug+ Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/hvppyplug/monorepo.git", "directory": "packages/ui-components"}}
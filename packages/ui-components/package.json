{"name": "@hvppyplug/ui-components", "version": "1.0.0", "description": "Shared UI components library for HVPPYPlug+ Expo React Native applications", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts", "./hooks": "./src/hooks/index.ts", "./utils": "./src/utils/index.ts", "./types": "./src/types/index.ts", "./config": "./src/config/index.ts", "./animations": "./src/animations/index.ts", "./icons": "./src/icons/index.ts", "./theme": "./src/config/theme.ts"}, "scripts": {"build": "node scripts/build.js", "build:watch": "node scripts/build.js --watch", "build:analyze": "node scripts/build.js --analyze", "build:types": "node scripts/build.js --types-only", "dev": "node scripts/dev.js", "clean": "node scripts/clean.js", "lint": "node scripts/lint.js", "lint:fix": "node scripts/lint.js --fix --format", "type-check": "node scripts/lint.js --type-check", "format": "node scripts/lint.js --format", "test": "node scripts/test.js", "test:watch": "node scripts/test.js --watch", "test:coverage": "node scripts/test.js --coverage", "test:unit": "node scripts/test.js --unit", "test:integration": "node scripts/test.js --integration", "test:a11y": "node scripts/test.js --a11y", "test:visual": "node scripts/test.js --visual", "docs:build": "node scripts/docs-build.js", "docs:dev": "node scripts/docs-dev.js", "docs:storybook": "node scripts/docs-build.js --storybook", "docs:api": "node scripts/docs-build.js --api", "docs:serve": "node scripts/docs-build.js --serve", "generate:component": "node scripts/generate-component.js", "health-check": "node scripts/health-check.js", "security-audit": "node scripts/security-audit.js"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@react-native-community/datetimepicker": "^8.2.0", "@tamagui/animations-react-native": "^1.112.21", "@tamagui/colors": "^1.112.21", "@tamagui/config": "^1.112.21", "@tamagui/core": "^1.112.21", "@tamagui/font-inter": "^1.112.21", "@tamagui/shorthands": "^1.112.21", "@tamagui/themes": "^1.112.21", "expo-blur": "~13.0.2", "expo-haptics": "~13.0.1", "lucide-react-native": "^0.447.0", "react-hook-form": "^7.53.0", "react-native-reanimated": "~3.10.1", "react-native-svg": "15.2.0", "tamagui": "^1.132.11", "zod": "^3.23.8"}, "devDependencies": {"@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@storybook/react-native": "^7.6.20", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.7.2", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "chalk": "^4.1.2", "chokidar": "^3.6.0", "eslint": "^8.57.0", "execa": "^5.1.1", "express": "^4.19.2", "fs-extra": "^11.2.0", "glob": "^10.3.10", "inquirer": "^8.2.6", "jest": "^29.7.0", "madge": "^7.0.0", "prettier": "^3.2.5", "rollup": "^4.13.0", "rollup-plugin-terser": "^7.0.2", "typedoc": "^0.25.12", "typescript": "~5.8.3", "yargs": "^17.7.2"}, "peerDependencies": {"expo": "~53.0.0", "react": "^19.0.0", "react-native": "^0.79.0"}, "keywords": ["react-native", "expo", "ui-components", "tamagui", "design-system", "hvppyplug"], "author": "HVPPYPlug+ Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/hvppyplug/monorepo.git", "directory": "packages/ui-components"}}
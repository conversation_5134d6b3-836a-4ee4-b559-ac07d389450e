# @hvppyplug/ui-components

A comprehensive shared UI components library for HVPPYPlug+ Expo React Native applications.

## Features

- 🎨 **Modern Design System** - Built with Tamagui for consistent theming and styling
- 🚀 **Performance Optimized** - Leverages React Native Reanimated for smooth animations
- 📱 **Expo Compatible** - Fully compatible with Expo SDK 53+
- 🔧 **TypeScript First** - Complete type safety and IntelliSense support
- 📋 **Form Handling** - Integrated React Hook Form with Zod validation
- 🎭 **Accessible** - Built with accessibility best practices
- 🧪 **Well Tested** - Comprehensive test coverage with Jest and React Native Testing Library

## Installation

```bash
# Install the package
pnpm add @hvppyplug/ui-components

# Install peer dependencies
pnpm add react react-native expo
```

## Quick Start

```tsx
import { TamaguiProvider } from '@tamagui/core'
import { config } from '@hvppyplug/ui-components/config'
import { Button, Card, Input } from '@hvppyplug/ui-components'

export default function App() {
  return (
    <TamaguiProvider config={config}>
      <Card padding="$4">
        <Input placeholder="Enter your name" />
        <Button onPress={() => console.log('Pressed!')}>
          Get Started
        </Button>
      </Card>
    </TamaguiProvider>
  )
}
```

## Components

### Core Components
- Button, Input, Card, Modal
- Typography (Text, Heading)
- Layout (Container, Stack, Grid)

### Form Components
- FormField, FormInput, FormSelect
- Validation with Zod schemas

### Feedback Components
- Toast, Loading, Alert
- Progress indicators

### Navigation Components
- Tab bars, Headers
- Navigation helpers

## Documentation

See the [documentation](./docs) folder for detailed component APIs and usage examples.

## Development

```bash
# Install dependencies
pnpm install

# Start development mode
pnpm dev

# Run tests
pnpm test

# Build the library
pnpm build
```

## License

MIT © HVPPYPlug+ Team

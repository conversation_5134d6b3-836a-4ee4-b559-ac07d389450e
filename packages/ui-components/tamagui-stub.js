// Tamagui stub module for development
import { styled } from '@tamagui/core';
import { View, Text as RNText, TouchableOpacity, TextInput } from 'react-native';
// Create basic styled components
export const XStack = styled(View, {
    name: 'XStack',
    flexDirection: 'row',
});
export const YStack = styled(View, {
    name: 'YStack',
    flexDirection: 'column',
});
export const Stack = styled(View, {
    name: 'Stack',
    flexDirection: 'column',
});
export const Text = styled(RNText, {
    name: 'Text',
});
export const Button = styled(TouchableOpacity, {
    name: 'Button',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
});
export const Input = styled(TextInput, {
    name: 'Input',
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
});
// Stub components
export const Checkbox = TouchableOpacity;
export const RadioGroup = View;
export const Switch = TouchableOpacity;
export const Sheet = View;
export const Spinner = View;
export const Progress = View;
export const Toast = View;
export const ToastProvider = View;
export const ToastViewport = View;
// Re-export from @tamagui/core
export { styled, TamaguiProvider, Theme, createTamagui, withStaticProperties } from '@tamagui/core';
//# sourceMappingURL=tamagui-stub.js.map
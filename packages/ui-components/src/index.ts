/**
 * @fileoverview Main entry point for the UI Components package
 *
 * This package provides a comprehensive set of React Native UI components
 * built with Tamagui, designed specifically for South African food delivery apps.
 *
 * Features:
 * - Consistent design system with theme support
 * - Accessibility-first components
 * - Form handling with validation
 * - Animation system
 * - Icon system
 * - Toast notifications
 * - Modal system
 * - Loading states
 * - Navigation components
 *
 * <AUTHOR> Agent
 * @version 1.0.0
 */

// Theme and configuration
export * from './config/theme'
export * from './types/theme'

// Core UI Components
export * from './components/Button'
export * from './components/Input'
export * from './components/Card'
export * from './components/Typography'
export * from './components/Layout'

// Form Components
export {
  FormProvider,
  FormInput,
  FormSelect,
  FormCheckbox,
  FormRadioGroup,
  FormSwitch,
  FormDatePicker,
  FormActions,
  FormSection,
  useFormContext
} from './components/Form'

// Feedback Components
export * from './components/Toast'
// Explicitly re-export LoadingProps to avoid ambiguity
export type { LoadingProps as UILoadingProps } from './components/Loading'
export { Loading } from './components/Loading'
export * from './components/Alert'
export * from './components/Modal'

// Navigation Components
export * from './components/Navigation'

// Icon System
export * from './icons'

// Animation System
export * from './animations'

// Utilities
export * from './utils/colors'
export * from './utils/spacing'
export * from './utils/typography'
export * from './utils/animations'
export * from './utils/validation'

// Hooks
export * from './hooks/useForm'

// Types (specific exports to avoid conflicts)
export type {
  ButtonProps,
  InputProps,
  CardProps,
  TextProps,
  ContainerProps,
  StackProps,
  IconProps as ComponentIconProps,
  ModalProps as ComponentModalProps,
} from './types/components'

export type {
  FormProviderProps,
  FormFieldProps as FormFieldPropsAlias,
  FormInputProps,
  FormSelectProps,
  FormCheckboxProps,
  FormRadioGroupProps,
  FormSwitchProps,
  FormDatePickerProps,
  FormActionsProps,
  FormSectionProps,
  FormValidationSchema,
  FormSubmissionState,
  FormConfig,
  FormFieldValidationRules,
  FormErrorMessage,
  FormFieldState,
  FormContextType,
  FormStepProps,
  FormStepperProps,
} from './types/forms'

// Re-export commonly used Tamagui components
export {
  TamaguiProvider,
  Theme,
  createTamagui,
  styled,
  withStaticProperties,
  XStack,
  YStack,
  Stack,
  Text,
  Button,
  Input,
  Checkbox,
  RadioGroup,
  Switch,
  Sheet,
  Spinner,
  Progress,
} from 'tamagui'

// Re-export additional Tamagui components
export {
  Toast as TamaguiToast,
  ToastProvider as TamaguiToastProvider,
  ToastViewport,
} from 'tamagui'

// Re-export form utilities
export { useForm, Controller } from 'react-hook-form'
export { z } from 'zod'
export { zodResolver } from '@hookform/resolvers/zod'

// Version
export const VERSION = '1.0.0'

// Package info
export const PACKAGE_INFO = {
  name: '@foodie/ui-components',
  version: VERSION,
  description: 'React Native UI components for South African food delivery apps',
  author: 'Augment Agent',
  license: 'MIT',
} as const

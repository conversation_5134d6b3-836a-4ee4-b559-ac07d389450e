import { View, Text as RNText, TextInput } from 'react-native';
export declare const XStack: import("@tamagui/core").TamaguiComponent<import("@tamagui/core").TamaDefer, View, import("@tamagui/core").TamaguiComponentPropsBaseBase & import("react-native").ViewProps, import("@tamagui/core").StackStyleBase, {}, import("@tamagui/core").StaticConfigPublic>;
export declare const YStack: import("@tamagui/core").TamaguiComponent<import("@tamagui/core").TamaDefer, View, import("@tamagui/core").TamaguiComponentPropsBaseBase & import("react-native").ViewProps, import("@tamagui/core").StackStyleBase, {}, import("@tamagui/core").StaticConfigPublic>;
export declare const Stack: import("@tamagui/core").TamaguiComponent<import("@tamagui/core").TamaDefer, View, import("@tamagui/core").TamaguiComponentPropsBaseBase & import("react-native").ViewProps, import("@tamagui/core").StackStyleBase, {}, import("@tamagui/core").StaticConfigPublic>;
export declare const Text: import("@tamagui/core").TamaguiComponent<import("@tamagui/core").TamaDefer, RNText, import("@tamagui/core").TamaguiComponentPropsBaseBase & import("react-native").TextProps, import("@tamagui/core").StackStyleBase, {}, import("@tamagui/core").StaticConfigPublic>;
export declare const Button: import("@tamagui/core").TamaguiComponent<import("@tamagui/core").TamaDefer, View, import("@tamagui/core").TamaguiComponentPropsBaseBase & import("react-native").TouchableOpacityProps & import("react").RefAttributes<View>, import("@tamagui/core").StackStyleBase, {}, import("@tamagui/core").StaticConfigPublic>;
export declare const Input: import("@tamagui/core").TamaguiComponent<import("@tamagui/core").TamaDefer, TextInput, import("@tamagui/core").TamaguiComponentPropsBaseBase & import("react-native").TextInputProps, import("@tamagui/core").StackStyleBase, {}, import("@tamagui/core").StaticConfigPublic>;
export declare const Checkbox: import("react").ForwardRefExoticComponent<import("react-native").TouchableOpacityProps & import("react").RefAttributes<View>>;
export declare const RadioGroup: typeof View;
export declare const Switch: import("react").ForwardRefExoticComponent<import("react-native").TouchableOpacityProps & import("react").RefAttributes<View>>;
export declare const Sheet: typeof View;
export declare const Spinner: typeof View;
export declare const Progress: typeof View;
export declare const Toast: typeof View;
export declare const ToastProvider: typeof View;
export declare const ToastViewport: typeof View;
export { styled, TamaguiProvider, Theme, createTamagui, withStaticProperties } from '@tamagui/core';
//# sourceMappingURL=tamagui-stub.d.ts.map
# Migration Guide: Tamagui to Gluestack UI v2

## Overview

This guide provides step-by-step instructions for migrating the HVPPYPlug UI components library from Tamagui to Gluestack UI v2, based on our comprehensive analysis and recommendations.

## 🎯 Migration Strategy: Gradual Transition

We recommend a **gradual migration approach** that allows both libraries to coexist during the transition period, minimizing risk and allowing for thorough testing.

## Phase 1: Foundation Setup (Week 1-2)

### 1.1 Install Gluestack UI v2 Dependencies

```bash
# Core Gluestack UI v2 packages
pnpm add @gluestack-ui/nativewind-utils
pnpm add @gluestack-ui/themed
pnpm add nativewind
pnpm add tailwindcss

# Development dependencies
pnpm add -D @gluestack-ui/config
pnpm add -D tailwindcss-animate
```

### 1.2 Configure NativeWind and Tailwind

Create `tailwind.config.js`:
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
  ],
  presets: [require("@gluestack-ui/config/tailwind.config")],
  theme: {
    extend: {
      // Custom theme extensions
    },
  },
  plugins: [],
}
```

Create `metro.config.js` updates:
```javascript
const { withNativeWind } = require('nativewind/metro');

module.exports = withNativeWind(config, { input: './global.css' });
```

### 1.3 Set Up Gluestack UI Provider

Create `src/components/ui/gluestack-ui-provider/index.tsx`:
```typescript
import React from 'react';
import { GluestackUIProvider as Provider } from '@gluestack-ui/themed';
import { config } from './config';

export function GluestackUIProvider({ children }: { children: React.ReactNode }) {
  return <Provider config={config}>{children}</Provider>;
}
```

### 1.4 Update Component Generation Scripts

Update `scripts/generate-component.js` to support both templates:
```javascript
const templates = {
  tamagui: 'basic-component',
  gluestack: 'gluestack-component',
};

// Add option to choose template type
.option('template-type', {
  describe: 'UI library template type',
  choices: ['tamagui', 'gluestack'],
  default: 'gluestack', // Default to new library
})
```

## Phase 2: Core Component Migration (Week 3-4)

### 2.1 Migration Priority Order

1. **Layout Components** (Box, Stack, Center)
2. **Typography** (Text, Heading)
3. **Form Components** (Button, Input, Checkbox)
4. **Feedback Components** (Alert, Toast, Spinner)

### 2.2 Component Migration Pattern

#### Before (Tamagui):
```typescript
import { styled } from '@tamagui/core';
import { View } from 'react-native';

const StyledButton = styled(View, {
  name: 'Button',
  backgroundColor: '$primary',
  padding: '$2',
  borderRadius: '$2',
  variants: {
    size: {
      small: { padding: '$1' },
      large: { padding: '$3' },
    }
  }
});

export const Button = ({ size = 'medium', children, ...props }) => (
  <StyledButton size={size} {...props}>
    {children}
  </StyledButton>
);
```

#### After (Gluestack UI v2):
```typescript
import { tva } from '@gluestack-ui/nativewind-utils/tva';
import { Pressable } from 'react-native';

const buttonStyle = tva({
  base: "bg-primary-500 rounded-md flex items-center justify-center",
  variants: {
    size: {
      small: "px-2 py-1",
      medium: "px-4 py-2", 
      large: "px-6 py-3",
    }
  }
});

export const Button = ({ size = 'medium', className, children, ...props }) => (
  <Pressable 
    className={buttonStyle({ size, class: className })} 
    {...props}
  >
    {children}
  </Pressable>
);
```

### 2.3 Coexistence Strategy

Create a component switcher to allow gradual migration:

```typescript
// src/components/ui/Button/index.ts
import { Button as TamaguiButton } from './Button.tamagui';
import { Button as GluestackButton } from './Button.gluestack';

// Environment flag to control which implementation to use
const USE_GLUESTACK = process.env.EXPO_PUBLIC_USE_GLUESTACK === 'true';

export const Button = USE_GLUESTACK ? GluestackButton : TamaguiButton;
export type ButtonProps = USE_GLUESTACK 
  ? React.ComponentProps<typeof GluestackButton>
  : React.ComponentProps<typeof TamaguiButton>;
```

## Phase 3: Complex Components (Week 5-6)

### 3.1 Overlay Components Migration

Focus on components with complex state management:
- Modal
- Drawer  
- Popover
- Tooltip
- AlertDialog

### 3.2 Animation Components

Migrate animation-heavy components:
- Accordion
- Actionsheet
- BottomSheet

### 3.3 Data Display Components

Migrate components with complex layouts:
- Card
- Table
- Avatar
- Badge

## Phase 4: Testing & Validation (Week 7-8)

### 4.1 Update Test Utilities

Create new test utilities for Gluestack UI:

```typescript
// src/test-utils/render-gluestack.tsx
import React from 'react';
import { render } from '@testing-library/react-native';
import { GluestackUIProvider } from '@/components/ui/gluestack-ui-provider';

export const renderWithGluestack = (component: React.ReactElement) => {
  return render(
    <GluestackUIProvider>
      {component}
    </GluestackUIProvider>
  );
};
```

### 4.2 Component Testing Strategy

1. **Parallel Testing**: Test both implementations during transition
2. **Visual Regression**: Ensure visual consistency
3. **Accessibility Testing**: Verify a11y compliance
4. **Performance Testing**: Compare bundle sizes and runtime performance

### 4.3 Update Storybook Configuration

Update Storybook to support Gluestack UI:

```javascript
// .storybook/preview.js
import { GluestackUIProvider } from '../src/components/ui/gluestack-ui-provider';

export const decorators = [
  (Story) => (
    <GluestackUIProvider>
      <Story />
    </GluestackUIProvider>
  ),
];
```

## Migration Checklist

### ✅ Pre-Migration
- [ ] Team training on NativeWind/Tailwind CSS
- [ ] Development environment setup
- [ ] Backup current implementation
- [ ] Create migration branch

### ✅ Phase 1: Foundation
- [ ] Install Gluestack UI v2 dependencies
- [ ] Configure NativeWind and Tailwind
- [ ] Set up Gluestack UI Provider
- [ ] Update build configuration
- [ ] Update component generation scripts

### ✅ Phase 2: Core Components
- [ ] Migrate layout components (Box, Stack, Center)
- [ ] Migrate typography components (Text, Heading)
- [ ] Migrate basic form components (Button, Input)
- [ ] Update theme configuration
- [ ] Test component coexistence

### ✅ Phase 3: Complex Components
- [ ] Migrate overlay components (Modal, Drawer, etc.)
- [ ] Migrate animation components (Accordion, etc.)
- [ ] Migrate data display components (Card, Table, etc.)
- [ ] Update advanced theming

### ✅ Phase 4: Testing & Validation
- [ ] Update all test files
- [ ] Run comprehensive test suite
- [ ] Perform visual regression testing
- [ ] Validate accessibility compliance
- [ ] Performance benchmarking
- [ ] Update documentation

### ✅ Post-Migration
- [ ] Remove Tamagui dependencies
- [ ] Clean up old component files
- [ ] Update CI/CD pipelines
- [ ] Team knowledge transfer
- [ ] Monitor production performance

## Common Migration Patterns

### Theme Token Migration

#### Tamagui Tokens:
```typescript
const theme = {
  colors: {
    primary: '#007AFF',
    secondary: '#5856D6',
  },
  space: {
    1: 4,
    2: 8,
    3: 16,
  }
};
```

#### Gluestack UI v2 Tokens:
```typescript
const config = {
  light: vars({
    '--color-primary-500': '#007AFF',
    '--color-secondary-500': '#5856D6',
  }),
  dark: vars({
    '--color-primary-500': '#0A84FF',
    '--color-secondary-500': '#6366F1',
  }),
};
```

### Styling Migration

#### From Tamagui styled():
```typescript
const StyledView = styled(View, {
  backgroundColor: '$primary',
  padding: '$2',
  borderRadius: '$2',
});
```

#### To Gluestack UI tva():
```typescript
const viewStyle = tva({
  base: "bg-primary-500 p-2 rounded-md",
});

<View className={viewStyle()} />
```

## Troubleshooting

### Common Issues

1. **NativeWind not working**: Ensure Metro configuration is correct
2. **Theme tokens not applied**: Check Tailwind config and CSS imports
3. **TypeScript errors**: Update type definitions and imports
4. **Build failures**: Verify all dependencies are correctly installed

### Performance Optimization

1. **Enable tree-shaking**: Use selective imports
2. **Optimize bundle size**: Remove unused Tailwind classes
3. **Cache configuration**: Set up proper Metro caching
4. **Monitor bundle size**: Use bundle analyzer tools

## Resources

- [Gluestack UI v2 Documentation](https://gluestack.io/ui/docs)
- [NativeWind Documentation](https://www.nativewind.dev/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Migration Support Discord](https://discord.gg/95qQ84nf6f)

## Support

For migration support and questions:
- Create issues in the project repository
- Join the Gluestack UI Discord community
- Consult the comprehensive documentation
- Review migration examples in the templates directory

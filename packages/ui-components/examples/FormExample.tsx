/**
 * Form Example
 * 
 * This file demonstrates form handling with validation using React Hook Form and Zod
 */

import React from 'react'
import { ScrollView } from 'react-native'
import {
  FormProvider,
  FormInput,
  FormSelect,
  FormCheckbox,
  FormRadioGroup,
  FormSwitch,
  FormDatePicker,
  FormActions,
  FormSection,
  Button,
  Card,
  Heading,
  Container,
  useForm,
  useToast,
} from '@hvppyplug/ui-components'
import { z } from 'zod'

// Define validation schema
const userRegistrationSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().regex(/^(\+27|0)[6-8][0-9]{8}$/, 'Please enter a valid South African phone number'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number'),
  confirmPassword: z.string(),
  dateOfBirth: z.date().max(new Date(), 'Birth date cannot be in the future'),
  province: z.enum(['EC', 'FS', 'GP', 'KZN', 'LP', 'MP', 'NC', 'NW', 'WC']),
  dietaryPreferences: z.array(z.string()).optional(),
  marketingEmails: z.boolean().default(false),
  termsAccepted: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type UserRegistrationData = z.infer<typeof userRegistrationSchema>

export function FormExample() {
  const toast = useToast()
  
  const form = useForm<UserRegistrationData>({
    schema: userRegistrationSchema,
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      province: 'GP',
      dietaryPreferences: [],
      marketingEmails: false,
      termsAccepted: false,
    },
  })

  const onSubmit = async (data: UserRegistrationData) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      console.log('Form submitted:', data)
      toast.success('Registration successful!', 'Welcome to our platform')
      
      // Reset form after successful submission
      form.reset()
    } catch (error) {
      toast.error('Registration failed', 'Please try again later')
    }
  }

  const provinceOptions = [
    { label: 'Eastern Cape', value: 'EC' },
    { label: 'Free State', value: 'FS' },
    { label: 'Gauteng', value: 'GP' },
    { label: 'KwaZulu-Natal', value: 'KZN' },
    { label: 'Limpopo', value: 'LP' },
    { label: 'Mpumalanga', value: 'MP' },
    { label: 'Northern Cape', value: 'NC' },
    { label: 'North West', value: 'NW' },
    { label: 'Western Cape', value: 'WC' },
  ]

  const dietaryOptions = [
    { label: 'Vegetarian', value: 'vegetarian' },
    { label: 'Vegan', value: 'vegan' },
    { label: 'Halal', value: 'halal' },
    { label: 'Kosher', value: 'kosher' },
    { label: 'Gluten-free', value: 'gluten-free' },
    { label: 'Dairy-free', value: 'dairy-free' },
  ]

  return (
    <ScrollView>
      <Container padding="large">
        <Card>
          <Heading level={1}>User Registration</Heading>
          
          <FormProvider form={form} onSubmit={onSubmit}>
            <FormSection 
              title="Personal Information"
              description="Please provide your basic information"
            >
              <FormInput
                name="firstName"
                label="First Name"
                placeholder="Enter your first name"
                required
              />
              
              <FormInput
                name="lastName"
                label="Last Name"
                placeholder="Enter your last name"
                required
              />
              
              <FormInput
                name="email"
                label="Email Address"
                placeholder="Enter your email"
                type="email"
                required
              />
              
              <FormInput
                name="phone"
                label="Phone Number"
                placeholder="Enter your phone number"
                type="phone"
                helperText="Format: 0XX XXX XXXX or +27XX XXX XXXX"
                required
              />
              
              <FormDatePicker
                name="dateOfBirth"
                label="Date of Birth"
                placeholder="Select your birth date"
                mode="date"
                maximumDate={new Date()}
                required
              />
              
              <FormSelect
                name="province"
                label="Province"
                placeholder="Select your province"
                options={provinceOptions}
                required
              />
            </FormSection>

            <FormSection 
              title="Account Security"
              description="Create a secure password for your account"
            >
              <FormInput
                name="password"
                label="Password"
                placeholder="Create a password"
                type="password"
                helperText="Must contain uppercase, lowercase, and numbers"
                required
              />
              
              <FormInput
                name="confirmPassword"
                label="Confirm Password"
                placeholder="Confirm your password"
                type="password"
                required
              />
            </FormSection>

            <FormSection 
              title="Preferences"
              description="Customize your experience"
            >
              <FormRadioGroup
                name="dietaryPreferences"
                label="Dietary Preferences"
                options={dietaryOptions}
                helperText="Select all that apply"
              />
              
              <FormSwitch
                name="marketingEmails"
                label="Receive marketing emails"
                helperText="Get updates about new restaurants and special offers"
              />
            </FormSection>

            <FormSection title="Terms and Conditions">
              <FormCheckbox
                name="termsAccepted"
                label="I accept the terms and conditions"
                required
              />
            </FormSection>

            <FormActions align="right" spacing="medium">
              <Button 
                variant="outline" 
                onPress={() => form.reset()}
              >
                Reset Form
              </Button>
              
              <Button 
                type="submit"
                loading={form.formState.isSubmitting}
                disabled={!form.formState.isValid}
              >
                {form.formState.isSubmitting ? 'Creating Account...' : 'Create Account'}
              </Button>
            </FormActions>
          </FormProvider>
        </Card>
      </Container>
    </ScrollView>
  )
}

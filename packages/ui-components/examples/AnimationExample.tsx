/**
 * Animation Examples
 * 
 * This file demonstrates the animation system capabilities
 */

import React, { useState } from 'react'
import { ScrollView } from 'react-native'
import {
  Button,
  Card,
  Text,
  Heading,
  Stack,
  Container,
  AnimatedView,
  FadeIn,
  FadeOut,
  SlideIn,
  SlideOut,
  ScaleIn,
  ScaleOut,
  BounceIn,
  BounceOut,
  FlipIn,
  FlipOut,
  RotateIn,
  RotateOut,
  ZoomIn,
  ZoomOut,
} from '@hvppyplug/ui-components'

export function AnimationExample() {
  const [showFade, setShowFade] = useState(true)
  const [showSlide, setShowSlide] = useState(true)
  const [showScale, setShowScale] = useState(true)
  const [showBounce, setShowBounce] = useState(true)
  const [showFlip, setShowFlip] = useState(true)
  const [showRotate, setShowRotate] = useState(true)
  const [showZoom, setShowZoom] = useState(true)
  const [triggerCustom, setTriggerCustom] = useState(false)

  return (
    <ScrollView>
      <Container padding="large">
        <Stack spacing={6}>
          <Heading level={1}>Animation Examples</Heading>

          {/* Fade Animations */}
          <Card>
            <Heading level={2}>Fade Animations</Heading>
            <Stack spacing={3}>
              <Button onPress={() => setShowFade(!showFade)}>
                Toggle Fade Animation
              </Button>
              
              {showFade ? (
                <FadeIn>
                  <Card variant="filled">
                    <Text>This card fades in when shown</Text>
                  </Card>
                </FadeIn>
              ) : (
                <FadeOut>
                  <Card variant="filled">
                    <Text>This card fades out when hidden</Text>
                  </Card>
                </FadeOut>
              )}
            </Stack>
          </Card>

          {/* Slide Animations */}
          <Card>
            <Heading level={2}>Slide Animations</Heading>
            <Stack spacing={3}>
              <Button onPress={() => setShowSlide(!showSlide)}>
                Toggle Slide Animation
              </Button>
              
              {showSlide && (
                <SlideIn direction="left">
                  <Card variant="elevated">
                    <Text>This card slides in from the left</Text>
                  </Card>
                </SlideIn>
              )}
              
              <SlideIn direction="right" autoPlay>
                <Card variant="elevated">
                  <Text>This card slides in from the right</Text>
                </Card>
              </SlideIn>
              
              <SlideIn direction="up" autoPlay>
                <Card variant="elevated">
                  <Text>This card slides in from the bottom</Text>
                </Card>
              </SlideIn>
              
              <SlideIn direction="down" autoPlay>
                <Card variant="elevated">
                  <Text>This card slides in from the top</Text>
                </Card>
              </SlideIn>
            </Stack>
          </Card>

          {/* Scale Animations */}
          <Card>
            <Heading level={2}>Scale Animations</Heading>
            <Stack spacing={3}>
              <Button onPress={() => setShowScale(!showScale)}>
                Toggle Scale Animation
              </Button>
              
              {showScale && (
                <ScaleIn>
                  <Card variant="outlined">
                    <Text>This card scales in</Text>
                  </Card>
                </ScaleIn>
              )}
            </Stack>
          </Card>

          {/* Bounce Animations */}
          <Card>
            <Heading level={2}>Bounce Animations</Heading>
            <Stack spacing={3}>
              <Button onPress={() => setShowBounce(!showBounce)}>
                Toggle Bounce Animation
              </Button>
              
              {showBounce && (
                <BounceIn>
                  <Card variant="filled">
                    <Text>This card bounces in</Text>
                  </Card>
                </BounceIn>
              )}
            </Stack>
          </Card>

          {/* Flip Animations */}
          <Card>
            <Heading level={2}>Flip Animations</Heading>
            <Stack spacing={3}>
              <Button onPress={() => setShowFlip(!showFlip)}>
                Toggle Flip Animation
              </Button>
              
              {showFlip && (
                <FlipIn>
                  <Card variant="elevated">
                    <Text>This card flips in</Text>
                  </Card>
                </FlipIn>
              )}
            </Stack>
          </Card>

          {/* Rotate Animations */}
          <Card>
            <Heading level={2}>Rotate Animations</Heading>
            <Stack spacing={3}>
              <Button onPress={() => setShowRotate(!showRotate)}>
                Toggle Rotate Animation
              </Button>
              
              {showRotate && (
                <RotateIn>
                  <Card variant="outlined">
                    <Text>This card rotates in</Text>
                  </Card>
                </RotateIn>
              )}
            </Stack>
          </Card>

          {/* Zoom Animations */}
          <Card>
            <Heading level={2}>Zoom Animations</Heading>
            <Stack spacing={3}>
              <Button onPress={() => setShowZoom(!showZoom)}>
                Toggle Zoom Animation
              </Button>
              
              {showZoom && (
                <ZoomIn>
                  <Card variant="filled">
                    <Text>This card zooms in</Text>
                  </Card>
                </ZoomIn>
              )}
            </Stack>
          </Card>

          {/* Custom Animations */}
          <Card>
            <Heading level={2}>Custom Animations</Heading>
            <Stack spacing={3}>
              <Button onPress={() => setTriggerCustom(!triggerCustom)}>
                Trigger Custom Animation
              </Button>
              
              <AnimatedView
                trigger={triggerCustom}
                animation={{
                  from: { opacity: 0, translateY: 50, scale: 0.8 },
                  to: { opacity: 1, translateY: 0, scale: 1 },
                  config: {
                    type: 'spring',
                    spring: {
                      damping: 15,
                      stiffness: 200,
                    },
                  },
                }}
                onAnimationStart={() => console.log('Custom animation started')}
                onAnimationEnd={() => console.log('Custom animation ended')}
              >
                <Card variant="elevated">
                  <Text>This card uses a custom animation</Text>
                  <Text variant="caption">
                    Combines opacity, translation, and scale
                  </Text>
                </Card>
              </AnimatedView>
              
              <AnimatedView
                preset="bounce-in"
                autoPlay
                iterations={3}
                direction="alternate"
              >
                <Card variant="filled">
                  <Text>This card bounces 3 times</Text>
                </Card>
              </AnimatedView>
              
              <AnimatedView
                preset="fade-in"
                autoPlay
                iterations={-1}
                direction="alternate"
              >
                <Card variant="outlined">
                  <Text>This card fades in and out infinitely</Text>
                </Card>
              </AnimatedView>
            </Stack>
          </Card>

          {/* Staggered Animations */}
          <Card>
            <Heading level={2}>Staggered Animations</Heading>
            <Stack spacing={3}>
              <Text variant="subtitle">
                Multiple cards with delayed animations
              </Text>
              
              {[1, 2, 3, 4].map((item, index) => (
                <AnimatedView
                  key={item}
                  preset="slide-in-left"
                  autoPlay
                  animation={{
                    config: {
                      type: 'spring',
                      delay: index * 200, // Stagger delay
                      spring: {
                        damping: 15,
                        stiffness: 150,
                      },
                    },
                  }}
                >
                  <Card variant="elevated">
                    <Text>Card {item} - Delayed by {index * 200}ms</Text>
                  </Card>
                </AnimatedView>
              ))}
            </Stack>
          </Card>

          {/* Button Press Animation */}
          <Card>
            <Heading level={2}>Interactive Animations</Heading>
            <Stack spacing={3}>
              <Text variant="subtitle">
                Buttons with press animations
              </Text>
              
              <AnimatedView preset="scale-in" autoPlay>
                <Button
                  onPress={() => console.log('Animated button pressed')}
                  variant="primary"
                >
                  Press me for feedback
                </Button>
              </AnimatedView>
              
              <Stack direction="row" spacing={3}>
                <AnimatedView preset="bounce-in" autoPlay>
                  <Button size="small" variant="outline">
                    Bounce
                  </Button>
                </AnimatedView>
                
                <AnimatedView preset="flip-in" autoPlay>
                  <Button size="small" variant="ghost">
                    Flip
                  </Button>
                </AnimatedView>
                
                <AnimatedView preset="rotate-in" autoPlay>
                  <Button size="small" variant="secondary">
                    Rotate
                  </Button>
                </AnimatedView>
              </Stack>
            </Stack>
          </Card>
        </Stack>
      </Container>
    </ScrollView>
  )
}

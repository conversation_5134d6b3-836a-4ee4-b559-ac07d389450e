/**
 * Basic Usage Examples
 * 
 * This file demonstrates basic usage of the UI components library
 */

import React from 'react'
import { ScrollView } from 'react-native'
import {
  Button,
  Input,
  Card,
  Text,
  Heading,
  Stack,
  Container,
  Icon,
  Alert,
  Loading,
} from '@hvppyplug/ui-components'

export function BasicUsageExample() {
  return (
    <ScrollView>
      <Container padding="large">
        <Stack spacing={6}>
          {/* Typography Examples */}
          <Card>
            <Heading level={2}>Typography</Heading>
            <Stack spacing={3}>
              <Text variant="display">Display Text</Text>
              <Text variant="heading">Heading Text</Text>
              <Text variant="title">Title Text</Text>
              <Text variant="subtitle">Subtitle Text</Text>
              <Text variant="body">Body text for regular content</Text>
              <Text variant="caption">Caption text for small details</Text>
            </Stack>
          </Card>

          {/* Button Examples */}
          <Card>
            <Heading level={2}>Buttons</Heading>
            <Stack spacing={3}>
              <Button variant="primary">Primary Button</Button>
              <Button variant="secondary">Secondary Button</Button>
              <Button variant="outline">Outline Button</Button>
              <Button variant="ghost">Ghost Button</Button>
              <Button variant="destructive">Destructive Button</Button>
              
              <Stack direction="row" spacing={3}>
                <Button size="small">Small</Button>
                <Button size="medium">Medium</Button>
                <Button size="large">Large</Button>
              </Stack>
              
              <Button loading>Loading Button</Button>
              <Button disabled>Disabled Button</Button>
              
              <Button leftIcon={<Icon name="heart" />}>
                With Left Icon
              </Button>
              
              <Button rightIcon={<Icon name="arrow-right" />}>
                With Right Icon
              </Button>
            </Stack>
          </Card>

          {/* Input Examples */}
          <Card>
            <Heading level={2}>Inputs</Heading>
            <Stack spacing={3}>
              <Input 
                label="Email"
                placeholder="Enter your email"
                type="email"
              />
              
              <Input 
                label="Password"
                placeholder="Enter your password"
                type="password"
                helperText="Must be at least 8 characters"
              />
              
              <Input 
                label="Phone Number"
                placeholder="Enter your phone number"
                type="phone"
                leftIcon={<Icon name="phone" />}
              />
              
              <Input 
                label="Search"
                placeholder="Search for restaurants..."
                rightIcon={<Icon name="search" />}
              />
              
              <Input 
                label="Message"
                placeholder="Enter your message"
                multiline
                numberOfLines={4}
              />
              
              <Input 
                label="Error Example"
                placeholder="This field has an error"
                error="This field is required"
              />
              
              <Input 
                label="Disabled Input"
                placeholder="This input is disabled"
                disabled
              />
            </Stack>
          </Card>

          {/* Card Examples */}
          <Card>
            <Heading level={2}>Cards</Heading>
            <Stack spacing={3}>
              <Card variant="default">
                <Text>Default Card</Text>
              </Card>
              
              <Card variant="elevated">
                <Text>Elevated Card</Text>
              </Card>
              
              <Card variant="outlined">
                <Text>Outlined Card</Text>
              </Card>
              
              <Card variant="filled">
                <Text>Filled Card</Text>
              </Card>
              
              <Card 
                pressable
                onPress={() => console.log('Card pressed')}
              >
                <Text>Pressable Card</Text>
              </Card>
              
              <Card
                header={<Text variant="title">Card with Header</Text>}
                footer={
                  <Stack direction="row" spacing={2}>
                    <Button size="small" variant="outline">Cancel</Button>
                    <Button size="small">Confirm</Button>
                  </Stack>
                }
              >
                <Text>This card has a header and footer</Text>
              </Card>
            </Stack>
          </Card>

          {/* Icon Examples */}
          <Card>
            <Heading level={2}>Icons</Heading>
            <Stack spacing={3}>
              <Stack direction="row" spacing={3} align="center">
                <Icon name="heart" size="small" />
                <Icon name="star" size="medium" />
                <Icon name="user" size="large" />
              </Stack>
              
              <Stack direction="row" spacing={3} align="center">
                <Icon name="home" color="$primary" />
                <Icon name="settings" color="$secondary" />
                <Icon name="bell" color="$error" />
                <Icon name="search" color="$success" />
              </Stack>
            </Stack>
          </Card>

          {/* Alert Examples */}
          <Card>
            <Heading level={2}>Alerts</Heading>
            <Stack spacing={3}>
              <Alert variant="success">
                Your order has been placed successfully!
              </Alert>
              
              <Alert variant="error" title="Error">
                Something went wrong. Please try again.
              </Alert>
              
              <Alert variant="warning" title="Warning">
                Your session will expire in 5 minutes.
              </Alert>
              
              <Alert variant="info" title="Information">
                New features are available in this update.
              </Alert>
              
              <Alert 
                variant="default"
                dismissible
                onClose={() => console.log('Alert dismissed')}
              >
                This alert can be dismissed.
              </Alert>
            </Stack>
          </Card>

          {/* Loading Examples */}
          <Card>
            <Heading level={2}>Loading States</Heading>
            <Stack spacing={3}>
              <Loading text="Loading..." />
              <Loading 
                text="Processing order..." 
                description="This may take a few moments"
              />
              <Loading size="small" showText={false} />
            </Stack>
          </Card>

          {/* Layout Examples */}
          <Card>
            <Heading level={2}>Layout</Heading>
            <Stack spacing={3}>
              <Text variant="subtitle">Horizontal Stack</Text>
              <Stack direction="row" spacing={2}>
                <Button size="small">Button 1</Button>
                <Button size="small">Button 2</Button>
                <Button size="small">Button 3</Button>
              </Stack>
              
              <Text variant="subtitle">Vertical Stack with Different Alignment</Text>
              <Stack spacing={2} align="center">
                <Button size="small">Centered Button</Button>
                <Text>Centered Text</Text>
              </Stack>
              
              <Text variant="subtitle">Justified Stack</Text>
              <Stack direction="row" justify="between">
                <Button size="small">Left</Button>
                <Button size="small">Right</Button>
              </Stack>
            </Stack>
          </Card>
        </Stack>
      </Container>
    </ScrollView>
  )
}

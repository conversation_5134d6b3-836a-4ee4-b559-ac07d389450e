#!/usr/bin/env node

/**
 * Fix All Components Script
 * Creates working implementations for all components using React Native primitives
 */

const fs = require('fs');
const path = require('path');

// Update all components to use React Native primitives instead of Tamagui
function updateComponentImports() {
  const componentUpdates = {
    'src/components/Button/Button.tsx': `import React from 'react'
import { TouchableOpacity, Text, ActivityIndicator, ViewStyle, TextStyle } from 'react-native'
import type { ButtonProps } from '../../types/components'

export const Button = React.forwardRef<TouchableOpacity, ButtonProps>(({
  children,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  leftIcon,
  rightIcon,
  onPress,
  style,
  ...props
}, ref) => {
  const buttonStyle: ViewStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingHorizontal: size === 'small' ? 12 : size === 'large' ? 20 : 16,
    paddingVertical: size === 'small' ? 8 : size === 'large' ? 16 : 12,
    backgroundColor: variant === 'primary' ? '#007AFF' : 
                    variant === 'secondary' ? '#8E8E93' :
                    variant === 'destructive' ? '#FF3B30' : 'transparent',
    borderWidth: variant === 'outline' ? 1 : 0,
    borderColor: '#007AFF',
    opacity: disabled ? 0.6 : 1,
    ...(style as ViewStyle),
  }

  const textStyle: TextStyle = {
    color: variant === 'outline' || variant === 'ghost' ? '#007AFF' : '#FFFFFF',
    fontSize: size === 'small' ? 14 : size === 'large' ? 18 : 16,
    fontWeight: '600',
  }

  return (
    <TouchableOpacity
      ref={ref}
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      {...props}
    >
      {leftIcon && !loading && leftIcon}
      {loading ? (
        <ActivityIndicator size="small" color={textStyle.color} />
      ) : (
        <Text style={textStyle}>{children}</Text>
      )}
      {rightIcon && !loading && rightIcon}
    </TouchableOpacity>
  )
})

Button.displayName = 'Button'
`,

    'src/components/Input/Input.tsx': `import React, { useState, forwardRef } from 'react'
import { TextInput, View, Text, ViewStyle, TextStyle } from 'react-native'
import type { InputProps } from '../../types/components'

export const Input = forwardRef<TextInput, InputProps>(({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  helperText,
  leftIcon,
  rightIcon,
  multiline = false,
  numberOfLines = 1,
  style,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false)

  const containerStyle: ViewStyle = {
    marginBottom: 16,
  }

  const labelStyle: TextStyle = {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  }

  const inputContainerStyle: ViewStyle = {
    flexDirection: 'row',
    alignItems: multiline ? 'flex-start' : 'center',
    borderWidth: 1,
    borderColor: error ? '#FF3B30' : isFocused ? '#007AFF' : '#C7C7CC',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: multiline ? 12 : 16,
    backgroundColor: '#FFFFFF',
  }

  const inputStyle: TextStyle = {
    flex: 1,
    fontSize: 16,
    color: '#000000',
    textAlignVertical: multiline ? 'top' : 'center',
    ...(style as TextStyle),
  }

  const helperStyle: TextStyle = {
    fontSize: 14,
    color: error ? '#FF3B30' : '#8E8E93',
    marginTop: 4,
  }

  return (
    <View style={containerStyle}>
      {label && <Text style={labelStyle}>{label}</Text>}
      <View style={inputContainerStyle}>
        {leftIcon && leftIcon}
        <TextInput
          ref={ref}
          style={inputStyle}
          placeholder={placeholder}
          value={value}
          onChangeText={onChangeText}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          multiline={multiline}
          numberOfLines={numberOfLines}
          {...props}
        />
        {rightIcon && rightIcon}
      </View>
      {(error || helperText) && (
        <Text style={helperStyle}>{error || helperText}</Text>
      )}
    </View>
  )
})

Input.displayName = 'Input'
`,

    'src/components/Card/Card.tsx': `import React from 'react'
import { View, TouchableOpacity, ViewStyle } from 'react-native'
import type { CardProps } from '../../types/components'

export const Card = React.forwardRef<View, CardProps>(({
  children,
  variant = 'default',
  pressable = false,
  onPress,
  style,
  ...props
}, ref) => {
  const cardStyle: ViewStyle = {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: variant === 'elevated' ? 0.1 : 0,
    shadowRadius: variant === 'elevated' ? 4 : 0,
    elevation: variant === 'elevated' ? 2 : 0,
    borderWidth: variant === 'outlined' ? 1 : 0,
    borderColor: '#E5E5EA',
    ...(style as ViewStyle),
  }

  if (pressable && onPress) {
    return (
      <TouchableOpacity
        ref={ref as any}
        style={cardStyle}
        onPress={onPress}
        {...props}
      >
        {children}
      </TouchableOpacity>
    )
  }

  return (
    <View ref={ref} style={cardStyle} {...props}>
      {children}
    </View>
  )
})

Card.displayName = 'Card'
`,

    'src/components/Typography/Text.tsx': `import React from 'react'
import { Text as RNText, TextStyle } from 'react-native'
import type { TextProps } from '../../types/components'

export const Text = React.forwardRef<RNText, TextProps>(({
  children,
  variant = 'body',
  style,
  ...props
}, ref) => {
  const getTextStyle = (): TextStyle => {
    switch (variant) {
      case 'display':
        return { fontSize: 32, fontWeight: '700', lineHeight: 38 }
      case 'heading':
        return { fontSize: 24, fontWeight: '600', lineHeight: 30 }
      case 'title':
        return { fontSize: 20, fontWeight: '600', lineHeight: 26 }
      case 'subtitle':
        return { fontSize: 18, fontWeight: '500', lineHeight: 24 }
      case 'body':
        return { fontSize: 16, fontWeight: '400', lineHeight: 22 }
      case 'caption':
        return { fontSize: 14, fontWeight: '400', lineHeight: 18 }
      default:
        return { fontSize: 16, fontWeight: '400', lineHeight: 22 }
    }
  }

  const textStyle: TextStyle = {
    color: '#000000',
    ...getTextStyle(),
    ...(style as TextStyle),
  }

  return (
    <RNText ref={ref} style={textStyle} {...props}>
      {children}
    </RNText>
  )
})

Text.displayName = 'Text'

export const Heading = React.forwardRef<RNText, TextProps & { level?: 1 | 2 | 3 | 4 | 5 | 6 }>(({
  children,
  level = 1,
  style,
  ...props
}, ref) => {
  const getHeadingStyle = (): TextStyle => {
    switch (level) {
      case 1: return { fontSize: 32, fontWeight: '700', lineHeight: 38 }
      case 2: return { fontSize: 28, fontWeight: '700', lineHeight: 34 }
      case 3: return { fontSize: 24, fontWeight: '600', lineHeight: 30 }
      case 4: return { fontSize: 20, fontWeight: '600', lineHeight: 26 }
      case 5: return { fontSize: 18, fontWeight: '500', lineHeight: 24 }
      case 6: return { fontSize: 16, fontWeight: '500', lineHeight: 22 }
      default: return { fontSize: 24, fontWeight: '600', lineHeight: 30 }
    }
  }

  const headingStyle: TextStyle = {
    color: '#000000',
    ...getHeadingStyle(),
    ...(style as TextStyle),
  }

  return (
    <RNText ref={ref} style={headingStyle} {...props}>
      {children}
    </RNText>
  )
})

Heading.displayName = 'Heading'
`
  }

  Object.entries(componentUpdates).forEach(([filePath, content]) => {
    const fullPath = path.join(__dirname, filePath)
    const dir = path.dirname(fullPath)
    
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
    
    fs.writeFileSync(fullPath, content, 'utf8')
    console.log(`✅ Updated ${filePath}`)
  })
}

// Create layout components
function createLayoutComponents() {
  const containerContent = `import React from 'react'
import { View, ViewStyle } from 'react-native'
import type { ContainerProps } from '../../types/components'

export const Container = React.forwardRef<View, ContainerProps>(({
  children,
  padding = 16,
  centered = false,
  style,
  ...props
}, ref) => {
  const containerStyle: ViewStyle = {
    flex: 1,
    padding: typeof padding === 'number' ? padding : 16,
    alignItems: centered ? 'center' : 'stretch',
    justifyContent: centered ? 'center' : 'flex-start',
    ...(style as ViewStyle),
  }

  return (
    <View ref={ref} style={containerStyle} {...props}>
      {children}
    </View>
  )
})

Container.displayName = 'Container'
`

  const stackContent = `import React from 'react'
import { View, ViewStyle } from 'react-native'
import type { StackProps } from '../../types/components'

export const Stack = React.forwardRef<View, StackProps>(({
  children,
  direction = 'column',
  spacing = 8,
  align = 'stretch',
  justify = 'flex-start',
  style,
  ...props
}, ref) => {
  const stackStyle: ViewStyle = {
    flexDirection: direction,
    alignItems: align,
    justifyContent: justify,
    gap: typeof spacing === 'number' ? spacing : 8,
    ...(style as ViewStyle),
  }

  return (
    <View ref={ref} style={stackStyle} {...props}>
      {children}
    </View>
  )
})

Stack.displayName = 'Stack'
`

  fs.writeFileSync(path.join(__dirname, 'src/components/Layout/Container.tsx'), containerContent, 'utf8')
  fs.writeFileSync(path.join(__dirname, 'src/components/Layout/Stack.tsx'), stackContent, 'utf8')
  console.log('✅ Created layout components')
}

// Main execution
console.log('🔧 Creating working components with React Native primitives...\n')

updateComponentImports()
createLayoutComponents()

console.log('\n✨ All components updated!')
console.log('📋 Components now use React Native primitives and should work without Tamagui')

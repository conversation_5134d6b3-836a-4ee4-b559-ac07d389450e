{"name": "hvppyplug-monorepo", "version": "1.0.0", "private": true, "description": "HVPPYPlug+ - Hyper-local on-demand snack delivery and services ecosystem for Soweto", "packageManager": "pnpm@8.15.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "scripts": {"build": "pnpm -r run build", "dev": "pnpm -r --parallel run dev", "clean": "pnpm -r run clean && rm -rf node_modules", "type-check": "pnpm -r run type-check", "lint": "pnpm -r run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "pnpm -r run test", "build:common": "pnpm --filter @hvppyplug/common build"}, "workspaces": ["packages/*", "apps/*", "backend"], "devDependencies": {"@types/node": "^20.11.24", "prettier": "^3.2.5", "typescript": "^5.3.3", "turbo": "^1.12.4"}, "keywords": ["expo", "react-native", "nextjs", "shadcn", "monorepo", "on-demand", "delivery", "soweto"], "author": "HVPPYPlug+ Team", "license": "MIT"}